# 🎥 React Video Player - Lecteur Vidéo Client

Une application web moderne et responsive pour visualiser et télécharger des vidéos stockées localement, développée avec React + Vite + TailwindCSS 4.

## 🚀 Fonctionnalités

- **📱 Mobile-First & Responsive** - Interface optimisée pour tous les appareils
- **🎬 Lecture Vidéo Intégrée** - Lecteur HTML5 avec contrôles personnalisés
- **📥 Téléchargement Direct** - Bouton de téléchargement pour chaque vidéo
- **📅 Tri Intelligent** - Classement automatique par date (plus récent en premier)
- **🏷️ Noms Lisibles** - Conversion automatique des noms de fichiers en format lisible
- **⚡ Performance Optimisée** - Chargement rapide et interface fluide

## 📋 Format des Fichiers Vidéo

Les vidéos doivent être nommées selon le format : `AAAA_MM_JJ_HH_MM_SS_ID.mp4`

**Exemple :**
- Fichier : `2025_06_21_00_23_49_01.mp4`
- Affiché : `Vidéo du 21 juin 2025 à 00:23:49 (ID 01)`

## 🛠️ Technologies Utilisées

- **Frontend :** React 18 + TypeScript
- **Build Tool :** Vite
- **Styling :** TailwindCSS 4
- **Lecteur Vidéo :** React-Player
- **Architecture :** Composants modulaires (DRY + SRP)

## 📁 Structure du Projet

```
react-video-player/
├── public/
│   └── videos/              # Dossier des fichiers vidéo
│       ├── 2025_06_21_00_23_49_01.mp4
│       └── 2025_06_20_15_30_22_02.mp4
├── src/
│   ├── components/
│   │   ├── VideoCard.tsx    # Carte individuelle de vidéo
│   │   ├── VideoList.tsx    # Liste des vidéos
│   │   ├── VideoPlayer.tsx  # Lecteur vidéo
│   │   └── DownloadButton.tsx # Bouton de téléchargement
│   ├── utils/
│   │   └── parseFilenameToDate.ts # Utilitaire de parsing
│   ├── App.tsx
│   └── main.tsx
├── README.md
├── PRD.md                   # Product Requirements Document
└── PLANNING.md              # Planning détaillé
```

## 🚀 Installation et Démarrage

### Prérequis
- Node.js 18+ 
- npm ou yarn

### Installation

```bash
# Cloner le projet
git clone <repository-url>
cd react-video-player

# Installer les dépendances
npm install

# Démarrer le serveur de développement
npm run dev
```

### Ajout de Vidéos

1. Placez vos fichiers `.mp4` dans le dossier `public/videos/`
2. Respectez le format de nommage : `AAAA_MM_JJ_HH_MM_SS_ID.mp4`
3. L'application détectera automatiquement les nouvelles vidéos

## 🎯 Utilisation

1. **Navigation :** Parcourez la liste des vidéos triées par date
2. **Lecture :** Cliquez sur une vidéo pour la lire
3. **Téléchargement :** Utilisez le bouton de téléchargement sur chaque carte
4. **Responsive :** L'interface s'adapte automatiquement à votre appareil

## 🧪 Tests

```bash
# Lancer les tests
npm run test

# Tests avec couverture
npm run test:coverage
```

## 📦 Build de Production

```bash
# Créer le build de production
npm run build

# Prévisualiser le build
npm run preview
```

## 🤝 Contribution

1. Fork le projet
2. Créez votre branche feature (`git checkout -b feature/AmazingFeature`)
3. Committez vos changements (`git commit -m 'Add some AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrez une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 🆘 Support

Pour toute question ou problème :
- Ouvrez une issue sur GitHub
- Consultez la documentation dans `PRD.md`
- Vérifiez le planning dans `PLANNING.md`

---

**Développé avec ❤️ pour une expérience vidéo optimale**
