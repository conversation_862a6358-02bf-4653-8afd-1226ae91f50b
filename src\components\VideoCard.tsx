import React, { useState, useCallback } from 'react';
import type { VideoCardProps } from '../types/video';
import DownloadButton from './DownloadButton';
import { formatDuration, formatFileSize } from '../utils/parseFilenameToDate';

/**
 * Composant VideoCard - Carte d'affichage pour une vidéo
 * Respecte le principe SRP (Single Responsibility Principle)
 */
const VideoCard: React.FC<VideoCardProps> = ({
  video,
  onPlay,
  onDownload,
  className = '',
  showThumbnail = true,
  showDuration = true,
  showFileSize = true
}) => {
  const [imageError, setImageError] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  /**
   * Gère le clic sur la carte pour lire la vidéo
   */
  const handleCardClick = useCallback(() => {
    onPlay?.(video);
  }, [video, onPlay]);

  /**
   * <PERSON><PERSON> le téléchargement de la vidéo
   */
  const handleDownload = useCallback((e: React.MouseEvent) => {
    e.stopPropagation(); // Empêche le déclenchement du clic sur la carte
    onDownload?.(video);
  }, [video, onDownload]);

  /**
   * Gère l'erreur de chargement de la thumbnail
   */
  const handleImageError = useCallback(() => {
    setImageError(true);
  }, []);

  /**
   * URL de la thumbnail (peut être générée ou par défaut)
   */
  const thumbnailUrl = video.thumbnail || `/videos/${video.filename}#t=1`;

  /**
   * Classes CSS pour la carte
   */
  const cardClasses = `
    group relative bg-white dark:bg-gray-800 rounded-xl shadow-md hover:shadow-xl 
    transition-all duration-300 cursor-pointer overflow-hidden border border-gray-200 
    dark:border-gray-700 ${isHovered ? 'scale-105' : ''} ${className}
  `.trim();

  return (
    <div 
      className={cardClasses}
      onClick={handleCardClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      role="button"
      tabIndex={0}
      aria-label={`Lire la vidéo ${video.displayName}`}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          handleCardClick();
        }
      }}
    >
      {/* Thumbnail / Image de prévisualisation */}
      {showThumbnail && (
        <div className="relative aspect-video bg-gray-100 dark:bg-gray-700 overflow-hidden">
          {!imageError ? (
            <img
              src={thumbnailUrl}
              alt={`Aperçu de ${video.displayName}`}
              className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
              onError={handleImageError}
              loading="lazy"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800">
              <div className="text-center text-gray-400 dark:text-gray-500">
                <svg className="w-16 h-16 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                <p className="text-sm font-medium">Aperçu non disponible</p>
              </div>
            </div>
          )}

          {/* Overlay de lecture */}
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
            <div className="transform scale-0 group-hover:scale-100 transition-transform duration-300">
              <div className="bg-white bg-opacity-90 rounded-full p-4 shadow-lg">
                <svg className="w-8 h-8 text-gray-800" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M8 5v14l11-7z"/>
                </svg>
              </div>
            </div>
          </div>

          {/* Badge de durée */}
          {showDuration && video.duration && (
            <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
              {formatDuration(video.duration)}
            </div>
          )}
        </div>
      )}

      {/* Contenu de la carte */}
      <div className="p-4">
        {/* Titre de la vidéo */}
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors" style={{ display: '-webkit-box', WebkitLineClamp: 2, WebkitBoxOrient: 'vertical', overflow: 'hidden' }}>
          {video.displayName}
        </h3>

        {/* Métadonnées */}
        <div className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
          {/* Date */}
          <div className="flex items-center">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <span>{video.date.toLocaleDateString('fr-FR', { 
              day: 'numeric', 
              month: 'long', 
              year: 'numeric' 
            })}</span>
          </div>

          {/* Heure */}
          <div className="flex items-center">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>{video.date.toLocaleTimeString('fr-FR', { 
              hour: '2-digit', 
              minute: '2-digit',
              second: '2-digit'
            })}</span>
          </div>

          {/* Taille du fichier */}
          {showFileSize && video.size && (
            <div className="flex items-center">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
              <span>{formatFileSize(video.size)}</span>
            </div>
          )}

          {/* ID de la vidéo */}
          <div className="flex items-center">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
            </svg>
            <span className="font-mono text-xs bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
              ID {video.id}
            </span>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
          {/* Bouton de lecture */}
          <button
            onClick={handleCardClick}
            className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            aria-label={`Lire ${video.displayName}`}
          >
            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M8 5v14l11-7z"/>
            </svg>
            Lire
          </button>

          {/* Bouton de téléchargement */}
          <div onClick={(e) => e.stopPropagation()}>
            <DownloadButton
              video={video}
              onDownloadStart={onDownload}
              variant="outline"
              size="sm"
              className="ml-2"
            />
          </div>
        </div>
      </div>

      {/* Indicateur de statut */}
      <div className="absolute top-2 left-2">
        {video.isValid ? (
          <div className="bg-green-500 text-white text-xs px-2 py-1 rounded-full flex items-center">
            <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 24 24">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            Valide
          </div>
        ) : (
          <div className="bg-red-500 text-white text-xs px-2 py-1 rounded-full flex items-center">
            <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
            </svg>
            Invalide
          </div>
        )}
      </div>

      {/* Effet de focus pour l'accessibilité */}
      <div className="absolute inset-0 rounded-xl ring-2 ring-transparent group-focus:ring-blue-500 pointer-events-none" />
    </div>
  );
};

export default VideoCard;
