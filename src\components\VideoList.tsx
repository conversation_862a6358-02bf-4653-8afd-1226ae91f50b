import React, { useMemo } from 'react';
import type { VideoListProps } from '../types/video';
import VideoCard from './VideoCard';

/**
 * Composant VideoList - Liste responsive des vidéos
 * Respecte le principe SRP (Single Responsibility Principle)
 */
const VideoList: React.FC<VideoListProps> = ({
  videos,
  onVideoSelect,
  onVideoDownload,
  loading = false,
  error = null,
  className = '',
  gridCols = {
    mobile: 1,
    tablet: 2,
    desktop: 3
  }
}) => {

  /**
   * Classes CSS pour la grille responsive
   */
  const gridClasses = useMemo(() => {
    const baseClasses = 'grid gap-6';
    const responsiveClasses = `
      grid-cols-${gridCols.mobile} 
      md:grid-cols-${gridCols.tablet} 
      lg:grid-cols-${gridCols.desktop}
    `;
    return `${baseClasses} ${responsiveClasses}`;
  }, [gridCols]);

  /**
   * Statistiques des vidéos
   */
  const stats = useMemo(() => {
    const validVideos = videos.filter(video => video.isValid);
    const totalDuration = validVideos.reduce((acc, video) => acc + (video.duration || 0), 0);
    const totalSize = validVideos.reduce((acc, video) => acc + (video.size || 0), 0);

    return {
      total: videos.length,
      valid: validVideos.length,
      invalid: videos.length - validVideos.length,
      totalDuration,
      totalSize
    };
  }, [videos]);

  /**
   * Formatage de la durée totale
   */
  const formatTotalDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}min`;
    }
    return `${minutes}min`;
  };

  /**
   * Formatage de la taille totale
   */
  const formatTotalSize = (bytes: number): string => {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  };

  /**
   * État de chargement
   */
  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        {/* Skeleton pour les statistiques */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="animate-pulse">
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="space-y-2">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
                  <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Skeleton pour les cartes vidéo */}
        <div className={gridClasses}>
          {[...Array(6)].map((_, index) => (
            <div key={index} className="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden border border-gray-200 dark:border-gray-700">
              <div className="animate-pulse">
                {/* Skeleton thumbnail */}
                <div className="aspect-video bg-gray-200 dark:bg-gray-700"></div>
                {/* Skeleton content */}
                <div className="p-4 space-y-3">
                  <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
                  </div>
                  <div className="flex justify-between pt-4">
                    <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
                    <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  /**
   * État d'erreur
   */
  if (error) {
    return (
      <div className={`flex items-center justify-center min-h-96 ${className}`}>
        <div className="text-center max-w-md mx-auto p-6">
          <div className="text-6xl mb-4">⚠️</div>
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            Erreur de chargement
          </h3>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            {error}
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Réessayer
          </button>
        </div>
      </div>
    );
  }

  /**
   * Aucune vidéo trouvée
   */
  if (videos.length === 0) {
    return (
      <div className={`flex items-center justify-center min-h-96 ${className}`}>
        <div className="text-center max-w-md mx-auto p-6">
          <div className="text-6xl mb-4">🎬</div>
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            Aucune vidéo trouvée
          </h3>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            Ajoutez des fichiers .mp4 dans le dossier <code className="bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm">/public/videos/</code> 
            en respectant le format de nommage <code className="bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm">AAAA_MM_JJ_HH_MM_SS_ID.mp4</code>
          </p>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            <p>Exemple : <code>2025_06_21_14_30_45_01.mp4</code></p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Statistiques */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
          Statistiques de la collection
        </h2>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {stats.total}
            </div>
            <div className="text-sm text-blue-600 dark:text-blue-400">
              Vidéos totales
            </div>
          </div>
          
          <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">
              {stats.valid}
            </div>
            <div className="text-sm text-green-600 dark:text-green-400">
              Vidéos valides
            </div>
          </div>
          
          {stats.totalDuration > 0 && (
            <div className="text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                {formatTotalDuration(stats.totalDuration)}
              </div>
              <div className="text-sm text-purple-600 dark:text-purple-400">
                Durée totale
              </div>
            </div>
          )}
          
          {stats.totalSize > 0 && (
            <div className="text-center p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
              <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                {formatTotalSize(stats.totalSize)}
              </div>
              <div className="text-sm text-orange-600 dark:text-orange-400">
                Taille totale
              </div>
            </div>
          )}
        </div>

        {stats.invalid > 0 && (
          <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <span className="text-sm text-yellow-800 dark:text-yellow-200">
                {stats.invalid} fichier(s) avec un format de nom invalide détecté(s)
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Grille des vidéos */}
      <div className={gridClasses}>
        {videos.map((video) => (
          <VideoCard
            key={video.filename}
            video={video}
            onPlay={onVideoSelect}
            onDownload={onVideoDownload}
            showThumbnail={true}
            showDuration={true}
            showFileSize={true}
          />
        ))}
      </div>

      {/* Message de fin */}
      {videos.length > 0 && (
        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
          <p className="text-sm">
            {stats.valid} vidéo(s) affichée(s) • Triées par date décroissante
          </p>
        </div>
      )}
    </div>
  );
};

export default VideoList;
