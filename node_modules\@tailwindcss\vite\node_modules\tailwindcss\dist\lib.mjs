import{a as et,b as D,c as I,d as T,e as oe,f as ke,g as it}from"./chunk-3FWCBO63.mjs";import{a as nt}from"./chunk-AZANAYY2.mjs";import{a as xe,b as tt,c as rt}from"./chunk-7ZRPRNCT.mjs";var ot="4.0.0";var ce=92,Ae=47,Ce=42,vr=34,yr=39,wr=58,$e=59,Q=10,de=32,Ve=9,lt=123,De=125,Ue=40,at=41,br=91,kr=93,st=45,_e=64,xr=33;function X(t){t=t.replaceAll(`\r
`,`
`);let r=[],n=[],e=[],o=null,s=null,a="",c="",d;for(let f=0;f<t.length;f++){let p=t.charCodeAt(f);if(p===ce)a+=t.slice(f,f+2),f+=1;else if(p===Ae&&t.charCodeAt(f+1)===Ce){let g=f;for(let y=f+2;y<t.length;y++)if(d=t.charCodeAt(y),d===ce)y+=1;else if(d===Ce&&t.charCodeAt(y+1)===Ae){f=y+1;break}let h=t.slice(g,f+1);h.charCodeAt(2)===xr&&n.push(Ne(h.slice(2,-2)))}else if(p===yr||p===vr){let g=f;for(let h=f+1;h<t.length;h++)if(d=t.charCodeAt(h),d===ce)h+=1;else if(d===p){f=h;break}else{if(d===$e&&t.charCodeAt(h+1)===Q)throw new Error(`Unterminated string: ${t.slice(g,h+1)+String.fromCharCode(p)}`);if(d===Q)throw new Error(`Unterminated string: ${t.slice(g,h)+String.fromCharCode(p)}`)}a+=t.slice(g,f+1)}else{if((p===de||p===Q||p===Ve)&&(d=t.charCodeAt(f+1))&&(d===de||d===Q||d===Ve))continue;if(p===Q){if(a.length===0)continue;d=a.charCodeAt(a.length-1),d!==de&&d!==Q&&d!==Ve&&(a+=" ")}else if(p===st&&t.charCodeAt(f+1)===st&&a.length===0){let g="",h=f,y=-1;for(let v=f+2;v<t.length;v++)if(d=t.charCodeAt(v),d===ce)v+=1;else if(d===Ae&&t.charCodeAt(v+1)===Ce){for(let x=v+2;x<t.length;x++)if(d=t.charCodeAt(x),d===ce)x+=1;else if(d===Ce&&t.charCodeAt(x+1)===Ae){v=x+1;break}}else if(y===-1&&d===wr)y=a.length+v-h;else if(d===$e&&g.length===0){a+=t.slice(h,v),f=v;break}else if(d===Ue)g+=")";else if(d===br)g+="]";else if(d===lt)g+="}";else if((d===De||t.length-1===v)&&g.length===0){f=v-1,a+=t.slice(h,v);break}else(d===at||d===kr||d===De)&&g.length>0&&t[v]===g[g.length-1]&&(g=g.slice(0,-1));let w=Fe(a,y);o?o.nodes.push(w):r.push(w),a=""}else if(p===$e&&a.charCodeAt(0)===_e)s=pe(a),o?o.nodes.push(s):r.push(s),a="",s=null;else if(p===$e&&c[c.length-1]!==")"){let g=Fe(a);o?o.nodes.push(g):r.push(g),a=""}else if(p===lt&&c[c.length-1]!==")")c+="}",s=M(a.trim()),o&&o.nodes.push(s),e.push(o),o=s,a="",s=null;else if(p===De&&c[c.length-1]!==")"){if(c==="")throw new Error("Missing opening {");if(c=c.slice(0,-1),a.length>0)if(a.charCodeAt(0)===_e)s=pe(a),o?o.nodes.push(s):r.push(s),a="",s=null;else{let h=a.indexOf(":");o&&o.nodes.push(Fe(a,h))}let g=e.pop()??null;g===null&&o&&r.push(o),o=g,a="",s=null}else if(p===Ue)c+=")",a+="(";else if(p===at){if(c[c.length-1]!==")")throw new Error("Missing opening (");c=c.slice(0,-1),a+=")"}else{if(a.length===0&&(p===de||p===Q||p===Ve))continue;a+=String.fromCharCode(p)}}}if(a.charCodeAt(0)===_e&&r.push(pe(a)),c.length>0&&o){if(o.kind==="rule")throw new Error(`Missing closing } at ${o.selector}`);if(o.kind==="at-rule")throw new Error(`Missing closing } at ${o.name} ${o.params}`)}return n.length>0?n.concat(r):r}function pe(t,r=[]){for(let n=5;n<t.length;n++){let e=t.charCodeAt(n);if(e===de||e===Ue){let o=t.slice(0,n).trim(),s=t.slice(n).trim();return O(o,s,r)}}return O(t.trim(),"",r)}function Fe(t,r=t.indexOf(":")){let n=t.indexOf("!important",r+1);return l(t.slice(0,r).trim(),t.slice(r+1,n===-1?t.length:n).trim(),n!==-1)}var Ar=64;function F(t,r=[]){return{kind:"rule",selector:t,nodes:r}}function O(t,r="",n=[]){return{kind:"at-rule",name:t,params:r,nodes:n}}function M(t,r=[]){return t.charCodeAt(0)===Ar?pe(t,r):F(t,r)}function l(t,r,n=!1){return{kind:"declaration",property:t,value:r,important:n}}function Ne(t){return{kind:"comment",value:t}}function ee(t,r){return{kind:"context",context:t,nodes:r}}function _(t){return{kind:"at-root",nodes:t}}function j(t,r,n=[],e={}){for(let o=0;o<t.length;o++){let s=t[o],a=n[n.length-1]??null;if(s.kind==="context"){if(j(s.nodes,r,n,{...e,...s.context})===2)return 2;continue}n.push(s);let c=r(s,{parent:a,context:e,path:n,replaceWith(d){Array.isArray(d)?d.length===0?t.splice(o,1):d.length===1?t[o]=d[0]:t.splice(o,1,...d):t[o]=d,o--}})??0;if(n.pop(),c===2)return 2;if(c!==1&&(s.kind==="rule"||s.kind==="at-rule")){n.push(s);let d=j(s.nodes,r,n,e);if(n.pop(),d===2)return 2}}}function Te(t,r,n=[],e={}){for(let o=0;o<t.length;o++){let s=t[o],a=n[n.length-1]??null;if(s.kind==="rule"||s.kind==="at-rule")n.push(s),Te(s.nodes,r,n,e),n.pop();else if(s.kind==="context"){Te(s.nodes,r,n,{...e,...s.context});continue}n.push(s),r(s,{parent:a,context:e,path:n,replaceWith(c){Array.isArray(c)?c.length===0?t.splice(o,1):c.length===1?t[o]=c[0]:t.splice(o,1,...c):t[o]=c,o+=c.length-1}}),n.pop()}}function te(t){let r=[],n=new Set;function e(s,a,c=0){if(s.kind==="declaration"){if(s.property==="--tw-sort"||s.value===void 0||s.value===null)return;a.push(s)}else if(s.kind==="rule")if(s.selector==="&")for(let d of s.nodes){let f=[];e(d,f,c+1),a.push(...f)}else{let d={...s,nodes:[]};for(let f of s.nodes)e(f,d.nodes,c+1);a.push(d)}else if(s.kind==="at-rule"&&s.name==="@property"&&c===0){if(n.has(s.params))return;n.add(s.params);let d={...s,nodes:[]};for(let f of s.nodes)e(f,d.nodes,c+1);a.push(d)}else if(s.kind==="at-rule"){let d={...s,nodes:[]};for(let f of s.nodes)e(f,d.nodes,c+1);a.push(d)}else if(s.kind==="at-root")for(let d of s.nodes){let f=[];e(d,f,0);for(let p of f)r.push(p)}else if(s.kind==="context"){if(s.context.reference)return;for(let d of s.nodes)e(d,a,c)}else s.kind==="comment"&&a.push(s)}let o=[];for(let s of t)e(s,o,0);return o.concat(r)}function J(t){function r(e,o=0){let s="",a="  ".repeat(o);if(e.kind==="declaration")s+=`${a}${e.property}: ${e.value}${e.important?" !important":""};
`;else if(e.kind==="rule"){s+=`${a}${e.selector} {
`;for(let c of e.nodes)s+=r(c,o+1);s+=`${a}}
`}else if(e.kind==="at-rule"){if(e.nodes.length===0)return`${a}${e.name} ${e.params};
`;s+=`${a}${e.name}${e.params?` ${e.params} `:" "}{
`;for(let c of e.nodes)s+=r(c,o+1);s+=`${a}}
`}else if(e.kind==="comment")s+=`${a}/*${e.value}*/
`;else if(e.kind==="context"||e.kind==="at-root")return"";return s}let n="";for(let e of t){let o=r(e);o!==""&&(n+=o)}return n}function ze(t){return{kind:"word",value:t}}function Cr(t,r){return{kind:"function",value:t,nodes:r}}function $r(t){return{kind:"separator",value:t}}function le(t,r,n=null){for(let e=0;e<t.length;e++){let o=t[e],s=r(o,{parent:n,replaceWith(a){Array.isArray(a)?a.length===0?t.splice(e,1):a.length===1?t[e]=a[0]:t.splice(e,1,...a):t[e]=a,e--}})??0;if(s===2)return 2;if(s!==1&&o.kind==="function"&&le(o.nodes,r,o)===2)return 2}}function B(t){let r="";for(let n of t)switch(n.kind){case"word":case"separator":{r+=n.value;break}case"function":r+=n.value+"("+B(n.nodes)+")"}return r}var Vr=92,Nr=41,ut=58,ft=44,Tr=34,ct=61,dt=62,pt=60,gt=10,Sr=40,Er=39,mt=47,ht=32,vt=9;function L(t){t=t.replaceAll(`\r
`,`
`);let r=[],n=[],e=null,o="",s;for(let a=0;a<t.length;a++){let c=t.charCodeAt(a);switch(c){case ut:case ft:case ct:case dt:case pt:case gt:case mt:case ht:case vt:{if(o.length>0){let g=ze(o);e?e.nodes.push(g):r.push(g),o=""}let d=a,f=a+1;for(;f<t.length&&(s=t.charCodeAt(f),!(s!==ut&&s!==ft&&s!==ct&&s!==dt&&s!==pt&&s!==gt&&s!==mt&&s!==ht&&s!==vt));f++);a=f-1;let p=$r(t.slice(d,f));e?e.nodes.push(p):r.push(p);break}case Er:case Tr:{let d=a;for(let f=a+1;f<t.length;f++)if(s=t.charCodeAt(f),s===Vr)f+=1;else if(s===c){a=f;break}o+=t.slice(d,a+1);break}case Sr:{let d=Cr(o,[]);o="",e?e.nodes.push(d):r.push(d),n.push(d),e=d;break}case Nr:{let d=n.pop();if(o.length>0){let f=ze(o);d.nodes.push(f),o=""}n.length>0?e=n[n.length-1]:e=null;break}default:o+=String.fromCharCode(c)}}return o.length>0&&r.push(ze(o)),r}function Y(t){if(t.indexOf("(")===-1)return ge(t);let r=L(t);return Me(r),t=B(r),t=et(t),t}function ge(t){let r="";for(let n=0;n<t.length;n++){let e=t[n];e==="\\"&&t[n+1]==="_"?(r+="_",n+=1):e==="_"?r+=" ":r+=e}return r}function Me(t){for(let r of t)switch(r.kind){case"function":{if(r.value==="url"||r.value.endsWith("_url")){r.value=ge(r.value);break}if(r.value==="var"||r.value.endsWith("_var")||r.value==="theme"||r.value.endsWith("_theme")){r.value=ge(r.value);for(let n=0;n<r.nodes.length;n++)n==0&&r.nodes[n].kind==="word"||Me([r.nodes[n]]);break}r.value=ge(r.value),Me(r.nodes);break}case"separator":case"word":{r.value=ge(r.value);break}default:Rr(r)}}function Rr(t){throw new Error(`Unexpected value: ${t}`)}var Kr=58,yt=45,wt=97,bt=122;function*kt(t,r){let n=D(t,":");if(r.theme.prefix){if(n.length===1||n[0]!==r.theme.prefix)return null;n.shift()}let e=n.pop(),o=[];for(let g=n.length-1;g>=0;--g){let h=r.parseVariant(n[g]);if(h===null)return;o.push(h)}let s=!1;e[e.length-1]==="!"?(s=!0,e=e.slice(0,-1)):e[0]==="!"&&(s=!0,e=e.slice(1)),r.utilities.has(e,"static")&&!e.includes("[")&&(yield{kind:"static",root:e,variants:o,important:s,raw:t});let[a,c=null,d]=D(e,"/");if(d)return;let f=c===null?null:Le(c);if(c!==null&&f===null)return;if(a[0]==="["){if(a[a.length-1]!=="]")return;let g=a.charCodeAt(1);if(g!==yt&&!(g>=wt&&g<=bt))return;a=a.slice(1,-1);let h=a.indexOf(":");if(h===-1||h===0||h===a.length-1)return;let y=a.slice(0,h),w=Y(a.slice(h+1));yield{kind:"arbitrary",property:y,value:w,modifier:f,variants:o,important:s,raw:t};return}let p;if(a[a.length-1]==="]"){let g=a.indexOf("-[");if(g===-1)return;let h=a.slice(0,g);if(!r.utilities.has(h,"functional"))return;let y=a.slice(g+1);p=[[h,y]]}else if(a[a.length-1]===")"){let g=a.indexOf("-(");if(g===-1)return;let h=a.slice(0,g);if(!r.utilities.has(h,"functional"))return;let y=a.slice(g+2,-1),w=D(y,":"),v=null;if(w.length===2&&(v=w[0],y=w[1]),y[0]!=="-"&&y[1]!=="-")return;p=[[h,v===null?`[var(${y})]`:`[${v}:var(${y})]`]]}else p=At(a,g=>r.utilities.has(g,"functional"));for(let[g,h]of p){let y={kind:"functional",root:g,modifier:f,value:null,variants:o,important:s,raw:t};if(h===null){yield y;continue}{let w=h.indexOf("[");if(w!==-1){if(h[h.length-1]!=="]")return;let x=Y(h.slice(w+1,-1)),V="";for(let S=0;S<x.length;S++){let R=x.charCodeAt(S);if(R===Kr){V=x.slice(0,S),x=x.slice(S+1);break}if(!(R===yt||R>=wt&&R<=bt))break}if(x.length===0||x.trim().length===0)continue;y.value={kind:"arbitrary",dataType:V||null,value:x}}else{let x=c===null||y.modifier?.kind==="arbitrary"?null:`${h}/${c}`;y.value={kind:"named",value:h,fraction:x}}}yield y}}function Le(t){if(t[0]==="["&&t[t.length-1]==="]"){let r=Y(t.slice(1,-1));return r.length===0||r.trim().length===0?null:{kind:"arbitrary",value:r}}if(t[0]==="("&&t[t.length-1]===")"){let r=Y(t.slice(1,-1));return r.length===0||r.trim().length===0?null:{kind:"arbitrary",value:`var(${r})`}}return{kind:"named",value:t}}function xt(t,r){if(t[0]==="["&&t[t.length-1]==="]"){if(t[1]==="@"&&t.includes("&"))return null;let n=Y(t.slice(1,-1));if(n.length===0||n.trim().length===0)return null;let e=n[0]===">"||n[0]==="+"||n[0]==="~";return!e&&n[0]!=="@"&&!n.includes("&")&&(n=`&:is(${n})`),{kind:"arbitrary",selector:n,relative:e}}{let[n,e=null,o]=D(t,"/");if(o)return null;let s=At(n,a=>r.variants.has(a));for(let[a,c]of s)switch(r.variants.kind(a)){case"static":return c!==null||e!==null?null:{kind:"static",root:a};case"functional":{let d=e===null?null:Le(e);if(e!==null&&d===null)return null;if(c===null)return{kind:"functional",root:a,modifier:d,value:null};if(c[c.length-1]==="]"){if(c[0]!=="[")continue;let f=Y(c.slice(1,-1));return f.length===0||f.trim().length===0?null:{kind:"functional",root:a,modifier:d,value:{kind:"arbitrary",value:f}}}if(c[c.length-1]===")"){if(c[0]!=="(")continue;let f=Y(c.slice(1,-1));return f.length===0||f.trim().length===0?null:{kind:"functional",root:a,modifier:d,value:{kind:"arbitrary",value:`var(${f})`}}}return{kind:"functional",root:a,modifier:d,value:{kind:"named",value:c}}}case"compound":{if(c===null)return null;let d=r.parseVariant(c);if(d===null||!r.variants.compoundsWith(a,d))return null;let f=e===null?null:Le(e);return e!==null&&f===null?null:{kind:"compound",root:a,modifier:f,variant:d}}}}return null}function*At(t,r){r(t)&&(yield[t,null]);let n=t.lastIndexOf("-");if(n===-1){t[0]==="@"&&r("@")&&(yield["@",t.slice(1)]);return}do{let e=t.slice(0,n);if(r(e)){let o=[e,t.slice(n+1)];if(o[1]==="")break;yield o}n=t.lastIndexOf("-",n-1)}while(n>0)}function re(t,r,n){if(t===r)return 0;let e=t.indexOf("("),o=r.indexOf("("),s=e===-1?t.replace(/[\d.]+/g,""):t.slice(0,e),a=o===-1?r.replace(/[\d.]+/g,""):r.slice(0,o),c=(s===a?0:s<a?-1:1)||(n==="asc"?parseInt(t)-parseInt(r):parseInt(r)-parseInt(t));return Number.isNaN(c)?t<r?-1:1:c}var z=class extends Map{constructor(n){super();this.factory=n}get(n){let e=super.get(n);return e===void 0&&(e=this.factory(n,this),this.set(n,e)),e}};var Pr=new Set(["inset","inherit","initial","revert","unset"]),Ct=/^-?(\d+|\.\d+)(.*?)$/g;function ne(t,r){return D(t,",").map(e=>{e=e.trim();let o=D(e," ").filter(f=>f.trim()!==""),s=null,a=null,c=null;for(let f of o)Pr.has(f)||(Ct.test(f)?(a===null?a=f:c===null&&(c=f),Ct.lastIndex=0):s===null&&(s=f));if(a===null||c===null)return e;let d=r(s??"currentcolor");return s!==null?e.replace(s,d):`${e} ${d}`}).join(", ")}var Or=/^-?[a-z][a-zA-Z0-9/%._-]*$/,jr=/^-?[a-z][a-zA-Z0-9/%._-]*-\*$/,We=class{utilities=new z(()=>[]);completions=new Map;static(r,n){this.utilities.get(r).push({kind:"static",compileFn:n})}functional(r,n,e){this.utilities.get(r).push({kind:"functional",compileFn:n,options:e})}has(r,n){return this.utilities.has(r)&&this.utilities.get(r).some(e=>e.kind===n)}get(r){return this.utilities.has(r)?this.utilities.get(r):[]}getCompletions(r){return this.completions.get(r)?.()??[]}suggest(r,n){this.completions.set(r,n)}keys(r){let n=[];for(let[e,o]of this.utilities.entries())for(let s of o)if(s.kind===r){n.push(e);break}return n}};function $(t,r,n){return O("@property",t,[l("syntax",n?`"${n}"`:'"*"'),l("inherits","false"),...r?[l("initial-value",r)]:[]])}function G(t,r){if(r===null)return t;let n=Number(r);return Number.isNaN(n)||(r=`${n*100}%`),`color-mix(in oklab, ${t} ${r}, transparent)`}function W(t,r,n){if(!r)return t;if(r.kind==="arbitrary")return G(t,r.value);let e=n.resolve(r.value,["--opacity"]);return e?G(t,e):ke(r.value)?G(t,`${r.value}%`):null}function q(t,r,n){let e=null;switch(t.value.value){case"inherit":{e="inherit";break}case"transparent":{e="transparent";break}case"current":{e="currentColor";break}default:{e=r.resolve(t.value.value,n);break}}return e?W(e,t.modifier,r):null}function Vt(t){let r=new We;function n(i,u){function*m(k){for(let b of t.keysInNamespaces(k))yield b.replaceAll("_",".")}r.suggest(i,()=>{let k=[];for(let b of u()){if(typeof b=="string"){k.push({values:[b],modifiers:[]});continue}let N=[...b.values??[],...m(b.valueThemeKeys??[])],K=[...b.modifiers??[],...m(b.modifierThemeKeys??[])];b.hasDefaultValue&&N.unshift(null),k.push({supportsNegative:b.supportsNegative,values:N,modifiers:K})}return k})}function e(i,u){r.static(i,()=>u.map(m=>typeof m=="function"?m():l(m[0],m[1])))}function o(i,u){function m({negative:k}){return b=>{let N=null;if(b.value)if(b.value.kind==="arbitrary"){if(b.modifier)return;N=b.value.value}else{if(N=t.resolve(b.value.fraction??b.value.value,u.themeKeys??[]),N===null&&u.supportsFractions&&b.value.fraction){let[K,C]=D(b.value.fraction,"/");if(!T(K)||!T(C))return;N=`calc(${b.value.fraction} * 100%)`}if(N===null&&k&&u.handleNegativeBareValue){if(N=u.handleNegativeBareValue(b.value),!N?.includes("/")&&b.modifier)return;if(N!==null)return u.handle(N)}if(N===null&&u.handleBareValue&&(N=u.handleBareValue(b.value),!N?.includes("/")&&b.modifier))return}else{if(b.modifier)return;N=u.defaultValue!==void 0?u.defaultValue:t.resolve(null,u.themeKeys??[])}if(N!==null)return u.handle(k?`calc(${N} * -1)`:N)}}u.supportsNegative&&r.functional(`-${i}`,m({negative:!0})),r.functional(i,m({negative:!1})),n(i,()=>[{supportsNegative:u.supportsNegative,valueThemeKeys:u.themeKeys??[],hasDefaultValue:u.defaultValue!==void 0&&u.defaultValue!==null}])}function s(i,u){r.functional(i,m=>{if(!m.value)return;let k=null;if(m.value.kind==="arbitrary"?(k=m.value.value,k=W(k,m.modifier,t)):k=q(m,t,u.themeKeys),k!==null)return u.handle(k)}),n(i,()=>[{values:["current","inherit","transparent"],valueThemeKeys:u.themeKeys,modifiers:Array.from({length:21},(m,k)=>`${k*5}`)}])}function a(i,u,m,{supportsNegative:k=!1,supportsFractions:b=!1}={}){k&&r.static(`-${i}-px`,()=>m("-1px")),r.static(`${i}-px`,()=>m("1px")),o(i,{themeKeys:u,supportsFractions:b,supportsNegative:k,defaultValue:null,handleBareValue:({value:N})=>{let K=t.resolve(null,["--spacing"]);return!K||!oe(N)?null:`calc(${K} * ${N})`},handleNegativeBareValue:({value:N})=>{let K=t.resolve(null,["--spacing"]);return!K||!oe(N)?null:`calc(${K} * -${N})`},handle:m}),n(i,()=>[{values:["0","0.5","1","1.5","2","2.5","3","3.5","4","5","6","7","8","9","10","11","12","14","16","20","24","28","32","36","40","44","48","52","56","60","64","72","80","96"],supportsNegative:k,valueThemeKeys:u}])}e("sr-only",[["position","absolute"],["width","1px"],["height","1px"],["padding","0"],["margin","-1px"],["overflow","hidden"],["clip","rect(0, 0, 0, 0)"],["white-space","nowrap"],["border-width","0"]]),e("not-sr-only",[["position","static"],["width","auto"],["height","auto"],["padding","0"],["margin","0"],["overflow","visible"],["clip","auto"],["white-space","normal"]]),e("pointer-events-none",[["pointer-events","none"]]),e("pointer-events-auto",[["pointer-events","auto"]]),e("visible",[["visibility","visible"]]),e("invisible",[["visibility","hidden"]]),e("collapse",[["visibility","collapse"]]),e("static",[["position","static"]]),e("fixed",[["position","fixed"]]),e("absolute",[["position","absolute"]]),e("relative",[["position","relative"]]),e("sticky",[["position","sticky"]]);for(let[i,u]of[["inset","inset"],["inset-x","inset-inline"],["inset-y","inset-block"],["start","inset-inline-start"],["end","inset-inline-end"],["top","top"],["right","right"],["bottom","bottom"],["left","left"]])e(`${i}-auto`,[[u,"auto"]]),e(`${i}-full`,[[u,"100%"]]),e(`-${i}-full`,[[u,"-100%"]]),a(i,["--inset","--spacing"],m=>[l(u,m)],{supportsNegative:!0,supportsFractions:!0});e("isolate",[["isolation","isolate"]]),e("isolation-auto",[["isolation","auto"]]),e("z-auto",[["z-index","auto"]]),o("z",{supportsNegative:!0,handleBareValue:({value:i})=>T(i)?i:null,themeKeys:["--z-index"],handle:i=>[l("z-index",i)]}),n("z",()=>[{supportsNegative:!0,values:["0","10","20","30","40","50"],valueThemeKeys:["--z-index"]}]),e("order-first",[["order","calc(-infinity)"]]),e("order-last",[["order","calc(infinity)"]]),e("order-none",[["order","0"]]),o("order",{supportsNegative:!0,handleBareValue:({value:i})=>T(i)?i:null,themeKeys:["--order"],handle:i=>[l("order",i)]}),n("order",()=>[{supportsNegative:!0,values:Array.from({length:12},(i,u)=>`${u+1}`),valueThemeKeys:["--order"]}]),e("col-auto",[["grid-column","auto"]]),o("col",{themeKeys:["--grid-column"],handle:i=>[l("grid-column",i)]}),e("col-span-full",[["grid-column","1 / -1"]]),o("col-span",{handleBareValue:({value:i})=>T(i)?i:null,handle:i=>[l("grid-column",`span ${i} / span ${i}`)]}),e("col-start-auto",[["grid-column-start","auto"]]),o("col-start",{supportsNegative:!0,handleBareValue:({value:i})=>T(i)?i:null,themeKeys:["--grid-column-start"],handle:i=>[l("grid-column-start",i)]}),e("col-end-auto",[["grid-column-end","auto"]]),o("col-end",{supportsNegative:!0,handleBareValue:({value:i})=>T(i)?i:null,themeKeys:["--grid-column-end"],handle:i=>[l("grid-column-end",i)]}),n("col-span",()=>[{values:Array.from({length:12},(i,u)=>`${u+1}`),valueThemeKeys:[]}]),n("col-start",()=>[{supportsNegative:!0,values:Array.from({length:13},(i,u)=>`${u+1}`),valueThemeKeys:["--grid-column-start"]}]),n("col-end",()=>[{supportsNegative:!0,values:Array.from({length:13},(i,u)=>`${u+1}`),valueThemeKeys:["--grid-column-end"]}]),e("row-auto",[["grid-row","auto"]]),o("row",{themeKeys:["--grid-row"],handle:i=>[l("grid-row",i)]}),e("row-span-full",[["grid-row","1 / -1"]]),o("row-span",{themeKeys:[],handleBareValue:({value:i})=>T(i)?i:null,handle:i=>[l("grid-row",`span ${i} / span ${i}`)]}),e("row-start-auto",[["grid-row-start","auto"]]),o("row-start",{supportsNegative:!0,handleBareValue:({value:i})=>T(i)?i:null,themeKeys:["--grid-row-start"],handle:i=>[l("grid-row-start",i)]}),e("row-end-auto",[["grid-row-end","auto"]]),o("row-end",{supportsNegative:!0,handleBareValue:({value:i})=>T(i)?i:null,themeKeys:["--grid-row-end"],handle:i=>[l("grid-row-end",i)]}),n("row-span",()=>[{values:Array.from({length:12},(i,u)=>`${u+1}`),valueThemeKeys:[]}]),n("row-start",()=>[{supportsNegative:!0,values:Array.from({length:13},(i,u)=>`${u+1}`),valueThemeKeys:["--grid-row-start"]}]),n("row-end",()=>[{supportsNegative:!0,values:Array.from({length:13},(i,u)=>`${u+1}`),valueThemeKeys:["--grid-row-end"]}]),e("float-start",[["float","inline-start"]]),e("float-end",[["float","inline-end"]]),e("float-right",[["float","right"]]),e("float-left",[["float","left"]]),e("float-none",[["float","none"]]),e("clear-start",[["clear","inline-start"]]),e("clear-end",[["clear","inline-end"]]),e("clear-right",[["clear","right"]]),e("clear-left",[["clear","left"]]),e("clear-both",[["clear","both"]]),e("clear-none",[["clear","none"]]);for(let[i,u]of[["m","margin"],["mx","margin-inline"],["my","margin-block"],["ms","margin-inline-start"],["me","margin-inline-end"],["mt","margin-top"],["mr","margin-right"],["mb","margin-bottom"],["ml","margin-left"]])e(`${i}-auto`,[[u,"auto"]]),a(i,["--margin","--spacing"],m=>[l(u,m)],{supportsNegative:!0});e("box-border",[["box-sizing","border-box"]]),e("box-content",[["box-sizing","content-box"]]),e("line-clamp-none",[["overflow","visible"],["display","block"],["-webkit-box-orient","horizontal"],["-webkit-line-clamp","unset"]]),o("line-clamp",{themeKeys:["--line-clamp"],handleBareValue:({value:i})=>T(i)?i:null,handle:i=>[l("overflow","hidden"),l("display","-webkit-box"),l("-webkit-box-orient","vertical"),l("-webkit-line-clamp",i)]}),n("line-clamp",()=>[{values:["1","2","3","4","5","6"],valueThemeKeys:["--line-clamp"]}]),e("block",[["display","block"]]),e("inline-block",[["display","inline-block"]]),e("inline",[["display","inline"]]),e("hidden",[["display","none"]]),e("inline-flex",[["display","inline-flex"]]),e("table",[["display","table"]]),e("inline-table",[["display","inline-table"]]),e("table-caption",[["display","table-caption"]]),e("table-cell",[["display","table-cell"]]),e("table-column",[["display","table-column"]]),e("table-column-group",[["display","table-column-group"]]),e("table-footer-group",[["display","table-footer-group"]]),e("table-header-group",[["display","table-header-group"]]),e("table-row-group",[["display","table-row-group"]]),e("table-row",[["display","table-row"]]),e("flow-root",[["display","flow-root"]]),e("flex",[["display","flex"]]),e("grid",[["display","grid"]]),e("inline-grid",[["display","inline-grid"]]),e("contents",[["display","contents"]]),e("list-item",[["display","list-item"]]),e("field-sizing-content",[["field-sizing","content"]]),e("field-sizing-fixed",[["field-sizing","fixed"]]),e("aspect-auto",[["aspect-ratio","auto"]]),e("aspect-square",[["aspect-ratio","1 / 1"]]),o("aspect",{themeKeys:["--aspect"],handleBareValue:({fraction:i})=>{if(i===null)return null;let[u,m]=D(i,"/");return!T(u)||!T(m)?null:i},handle:i=>[l("aspect-ratio",i)]});for(let[i,u]of[["auto","auto"],["full","100%"],["svw","100svw"],["lvw","100lvw"],["dvw","100dvw"],["svh","100svh"],["lvh","100lvh"],["dvh","100dvh"],["min","min-content"],["max","max-content"],["fit","fit-content"]])e(`size-${i}`,[["--tw-sort","size"],["width",u],["height",u]]),e(`w-${i}`,[["width",u]]),e(`min-w-${i}`,[["min-width",u]]),e(`max-w-${i}`,[["max-width",u]]),e(`h-${i}`,[["height",u]]),e(`min-h-${i}`,[["min-height",u]]),e(`max-h-${i}`,[["max-height",u]]);e("w-screen",[["width","100vw"]]),e("min-w-screen",[["min-width","100vw"]]),e("max-w-screen",[["max-width","100vw"]]),e("h-screen",[["height","100vh"]]),e("min-h-screen",[["min-height","100vh"]]),e("max-h-screen",[["max-height","100vh"]]),e("min-w-none",[["min-width","none"]]),e("max-w-none",[["max-width","none"]]),e("min-h-none",[["min-height","none"]]),e("max-h-none",[["max-height","none"]]),a("size",["--size","--spacing"],i=>[l("--tw-sort","size"),l("width",i),l("height",i)],{supportsFractions:!0});for(let[i,u,m]of[["w",["--width","--spacing","--container"],"width"],["min-w",["--min-width","--spacing","--container"],"min-width"],["max-w",["--max-width","--spacing","--container"],"max-width"],["h",["--height","--spacing"],"height"],["min-h",["--min-height","--height","--spacing"],"min-height"],["max-h",["--max-height","--height","--spacing"],"max-height"]])a(i,u,k=>[l(m,k)],{supportsFractions:!0});r.static("container",()=>{let i=[...t.namespace("--breakpoint").values()];i.sort((m,k)=>re(m,k,"asc"));let u=[l("--tw-sort","--tw-container-component"),l("width","100%")];for(let m of i)u.push(O("@media",`(width >= ${m})`,[l("max-width",m)]));return u}),e("flex-auto",[["flex","auto"]]),e("flex-initial",[["flex","0 auto"]]),e("flex-none",[["flex","none"]]),r.functional("flex",i=>{if(i.value){if(i.value.kind==="arbitrary")return i.modifier?void 0:[l("flex",i.value.value)];if(i.value.fraction){let[u,m]=D(i.value.fraction,"/");return!T(u)||!T(m)?void 0:[l("flex",`calc(${i.value.fraction} * 100%)`)]}if(T(i.value.value))return i.modifier?void 0:[l("flex",i.value.value)]}}),o("shrink",{defaultValue:"1",handleBareValue:({value:i})=>T(i)?i:null,handle:i=>[l("flex-shrink",i)]}),o("grow",{defaultValue:"1",handleBareValue:({value:i})=>T(i)?i:null,handle:i=>[l("flex-grow",i)]}),n("shrink",()=>[{values:["0"],valueThemeKeys:[],hasDefaultValue:!0}]),n("grow",()=>[{values:["0"],valueThemeKeys:[],hasDefaultValue:!0}]),e("basis-auto",[["flex-basis","auto"]]),e("basis-full",[["flex-basis","100%"]]),a("basis",["--flex-basis","--spacing","--container"],i=>[l("flex-basis",i)],{supportsFractions:!0}),e("table-auto",[["table-layout","auto"]]),e("table-fixed",[["table-layout","fixed"]]),e("caption-top",[["caption-side","top"]]),e("caption-bottom",[["caption-side","bottom"]]),e("border-collapse",[["border-collapse","collapse"]]),e("border-separate",[["border-collapse","separate"]]);let c=()=>_([$("--tw-border-spacing-x","0","<length>"),$("--tw-border-spacing-y","0","<length>")]);a("border-spacing",["--border-spacing","--spacing"],i=>[c(),l("--tw-border-spacing-x",i),l("--tw-border-spacing-y",i),l("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),a("border-spacing-x",["--border-spacing","--spacing"],i=>[c(),l("--tw-border-spacing-x",i),l("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),a("border-spacing-y",["--border-spacing","--spacing"],i=>[c(),l("--tw-border-spacing-y",i),l("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),e("origin-center",[["transform-origin","center"]]),e("origin-top",[["transform-origin","top"]]),e("origin-top-right",[["transform-origin","top right"]]),e("origin-right",[["transform-origin","right"]]),e("origin-bottom-right",[["transform-origin","bottom right"]]),e("origin-bottom",[["transform-origin","bottom"]]),e("origin-bottom-left",[["transform-origin","bottom left"]]),e("origin-left",[["transform-origin","left"]]),e("origin-top-left",[["transform-origin","top left"]]),o("origin",{themeKeys:["--transform-origin"],handle:i=>[l("transform-origin",i)]}),e("perspective-origin-center",[["perspective-origin","center"]]),e("perspective-origin-top",[["perspective-origin","top"]]),e("perspective-origin-top-right",[["perspective-origin","top right"]]),e("perspective-origin-right",[["perspective-origin","right"]]),e("perspective-origin-bottom-right",[["perspective-origin","bottom right"]]),e("perspective-origin-bottom",[["perspective-origin","bottom"]]),e("perspective-origin-bottom-left",[["perspective-origin","bottom left"]]),e("perspective-origin-left",[["perspective-origin","left"]]),e("perspective-origin-top-left",[["perspective-origin","top left"]]),o("perspective-origin",{themeKeys:["--perspective-origin"],handle:i=>[l("perspective-origin",i)]}),e("perspective-none",[["perspective","none"]]),o("perspective",{themeKeys:["--perspective"],handle:i=>[l("perspective",i)]});let d=()=>_([$("--tw-translate-x","0"),$("--tw-translate-y","0"),$("--tw-translate-z","0")]);e("translate-none",[["translate","none"]]),e("-translate-full",[d,["--tw-translate-x","-100%"],["--tw-translate-y","-100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),e("translate-full",[d,["--tw-translate-x","100%"],["--tw-translate-y","100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),a("translate",["--translate","--spacing"],i=>[d(),l("--tw-translate-x",i),l("--tw-translate-y",i),l("translate","var(--tw-translate-x) var(--tw-translate-y)")],{supportsNegative:!0,supportsFractions:!0});for(let i of["x","y"])e(`-translate-${i}-full`,[d,[`--tw-translate-${i}`,"-100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),e(`translate-${i}-full`,[d,[`--tw-translate-${i}`,"100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),a(`translate-${i}`,["--translate","--spacing"],u=>[d(),l(`--tw-translate-${i}`,u),l("translate","var(--tw-translate-x) var(--tw-translate-y)")],{supportsNegative:!0,supportsFractions:!0});a("translate-z",["--translate","--spacing"],i=>[d(),l("--tw-translate-z",i),l("translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)")],{supportsNegative:!0}),e("-translate-z-px",[d,["--tw-translate-z","-1px"],["translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)"]]),e("translate-z-px",[d,["--tw-translate-z","1px"],["translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)"]]),e("translate-3d",[d,["translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)"]]);let f=()=>_([$("--tw-scale-x","1"),$("--tw-scale-y","1"),$("--tw-scale-z","1")]);e("scale-none",[["scale","none"]]);function p({negative:i}){return u=>{if(!u.value||u.modifier)return;let m;return u.value.kind==="arbitrary"?(m=u.value.value,[l("scale",m)]):(m=t.resolve(u.value.value,["--scale"]),!m&&T(u.value.value)&&(m=`${u.value.value}%`),m?(m=i?`calc(${m} * -1)`:m,[f(),l("--tw-scale-x",m),l("--tw-scale-y",m),l("--tw-scale-z",m),l("scale","var(--tw-scale-x) var(--tw-scale-y)")]):void 0)}}r.functional("-scale",p({negative:!0})),r.functional("scale",p({negative:!1})),n("scale",()=>[{supportsNegative:!0,values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--scale"]}]);for(let i of["x","y","z"])o(`scale-${i}`,{supportsNegative:!0,themeKeys:["--scale"],handleBareValue:({value:u})=>T(u)?`${u}%`:null,handle:u=>[f(),l(`--tw-scale-${i}`,u),l("scale",`var(--tw-scale-x) var(--tw-scale-y)${i==="z"?" var(--tw-scale-z)":""}`)]}),n(`scale-${i}`,()=>[{supportsNegative:!0,values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--scale"]}]);e("scale-3d",[f,["scale","var(--tw-scale-x) var(--tw-scale-y) var(--tw-scale-z)"]]),e("rotate-none",[["rotate","none"]]);function g({negative:i}){return u=>{if(!u.value||u.modifier)return;let m;if(u.value.kind==="arbitrary"){m=u.value.value;let k=u.value.dataType??I(m,["angle","vector"]);if(k==="vector")return[l("rotate",`${m} var(--tw-rotate)`)];if(k!=="angle")return[l("rotate",m)]}else if(m=t.resolve(u.value.value,["--rotate"]),!m&&T(u.value.value)&&(m=`${u.value.value}deg`),!m)return;return[l("rotate",i?`calc(${m} * -1)`:m)]}}r.functional("-rotate",g({negative:!0})),r.functional("rotate",g({negative:!1})),n("rotate",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"],valueThemeKeys:["--rotate"]}]);{let i=["var(--tw-rotate-x)","var(--tw-rotate-y)","var(--tw-rotate-z)","var(--tw-skew-x)","var(--tw-skew-y)"].join(" "),u=()=>_([$("--tw-rotate-x","rotateX(0)"),$("--tw-rotate-y","rotateY(0)"),$("--tw-rotate-z","rotateZ(0)"),$("--tw-skew-x","skewX(0)"),$("--tw-skew-y","skewY(0)")]);for(let m of["x","y","z"])o(`rotate-${m}`,{supportsNegative:!0,themeKeys:["--rotate"],handleBareValue:({value:k})=>T(k)?`${k}deg`:null,handle:k=>[u(),l(`--tw-rotate-${m}`,`rotate${m.toUpperCase()}(${k})`),l("transform",i)]}),n(`rotate-${m}`,()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"],valueThemeKeys:["--rotate"]}]);o("skew",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:m})=>T(m)?`${m}deg`:null,handle:m=>[u(),l("--tw-skew-x",`skewX(${m})`),l("--tw-skew-y",`skewY(${m})`),l("transform",i)]}),o("skew-x",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:m})=>T(m)?`${m}deg`:null,handle:m=>[u(),l("--tw-skew-x",`skewX(${m})`),l("transform",i)]}),o("skew-y",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:m})=>T(m)?`${m}deg`:null,handle:m=>[u(),l("--tw-skew-y",`skewY(${m})`),l("transform",i)]}),n("skew",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),n("skew-x",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),n("skew-y",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),r.functional("transform",m=>{if(m.modifier)return;let k=null;if(m.value?m.value.kind==="arbitrary"&&(k=m.value.value):k=i,k!==null)return[u(),l("transform",k)]}),n("transform",()=>[{hasDefaultValue:!0}]),e("transform-cpu",[["transform",i]]),e("transform-gpu",[["transform",`translateZ(0) ${i}`]]),e("transform-none",[["transform","none"]])}e("transform-flat",[["transform-style","flat"]]),e("transform-3d",[["transform-style","preserve-3d"]]),e("transform-content",[["transform-box","content-box"]]),e("transform-border",[["transform-box","border-box"]]),e("transform-fill",[["transform-box","fill-box"]]),e("transform-stroke",[["transform-box","stroke-box"]]),e("transform-view",[["transform-box","view-box"]]),e("backface-visible",[["backface-visibility","visible"]]),e("backface-hidden",[["backface-visibility","hidden"]]);for(let i of["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out"])e(`cursor-${i}`,[["cursor",i]]);o("cursor",{themeKeys:["--cursor"],handle:i=>[l("cursor",i)]});for(let i of["auto","none","manipulation"])e(`touch-${i}`,[["touch-action",i]]);let h=()=>_([$("--tw-pan-x"),$("--tw-pan-y"),$("--tw-pinch-zoom")]);for(let i of["x","left","right"])e(`touch-pan-${i}`,[h,["--tw-pan-x",`pan-${i}`],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);for(let i of["y","up","down"])e(`touch-pan-${i}`,[h,["--tw-pan-y",`pan-${i}`],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);e("touch-pinch-zoom",[h,["--tw-pinch-zoom","pinch-zoom"],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);for(let i of["none","text","all","auto"])e(`select-${i}`,[["-webkit-user-select",i],["user-select",i]]);e("resize-none",[["resize","none"]]),e("resize-x",[["resize","horizontal"]]),e("resize-y",[["resize","vertical"]]),e("resize",[["resize","both"]]),e("snap-none",[["scroll-snap-type","none"]]);let y=()=>_([$("--tw-scroll-snap-strictness","proximity","*")]);for(let i of["x","y","both"])e(`snap-${i}`,[y,["scroll-snap-type",`${i} var(--tw-scroll-snap-strictness)`]]);e("snap-mandatory",[y,["--tw-scroll-snap-strictness","mandatory"]]),e("snap-proximity",[y,["--tw-scroll-snap-strictness","proximity"]]),e("snap-align-none",[["scroll-snap-align","none"]]),e("snap-start",[["scroll-snap-align","start"]]),e("snap-end",[["scroll-snap-align","end"]]),e("snap-center",[["scroll-snap-align","center"]]),e("snap-normal",[["scroll-snap-stop","normal"]]),e("snap-always",[["scroll-snap-stop","always"]]);for(let[i,u]of[["scroll-m","scroll-margin"],["scroll-mx","scroll-margin-inline"],["scroll-my","scroll-margin-block"],["scroll-ms","scroll-margin-inline-start"],["scroll-me","scroll-margin-inline-end"],["scroll-mt","scroll-margin-top"],["scroll-mr","scroll-margin-right"],["scroll-mb","scroll-margin-bottom"],["scroll-ml","scroll-margin-left"]])a(i,["--scroll-margin","--spacing"],m=>[l(u,m)],{supportsNegative:!0});for(let[i,u]of[["scroll-p","scroll-padding"],["scroll-px","scroll-padding-inline"],["scroll-py","scroll-padding-block"],["scroll-ps","scroll-padding-inline-start"],["scroll-pe","scroll-padding-inline-end"],["scroll-pt","scroll-padding-top"],["scroll-pr","scroll-padding-right"],["scroll-pb","scroll-padding-bottom"],["scroll-pl","scroll-padding-left"]])a(i,["--scroll-padding","--spacing"],m=>[l(u,m)]);e("list-inside",[["list-style-position","inside"]]),e("list-outside",[["list-style-position","outside"]]),e("list-none",[["list-style-type","none"]]),e("list-disc",[["list-style-type","disc"]]),e("list-decimal",[["list-style-type","decimal"]]),o("list",{themeKeys:["--list-style-type"],handle:i=>[l("list-style-type",i)]}),e("list-image-none",[["list-style-image","none"]]),o("list-image",{themeKeys:["--list-style-image"],handle:i=>[l("list-style-image",i)]}),e("appearance-none",[["appearance","none"]]),e("appearance-auto",[["appearance","auto"]]),e("scheme-normal",[["color-scheme","normal"]]),e("scheme-dark",[["color-scheme","dark"]]),e("scheme-light",[["color-scheme","light"]]),e("scheme-light-dark",[["color-scheme","light dark"]]),e("scheme-only-dark",[["color-scheme","only dark"]]),e("scheme-only-light",[["color-scheme","only light"]]),e("columns-auto",[["columns","auto"]]),o("columns",{themeKeys:["--columns","--container"],handleBareValue:({value:i})=>T(i)?i:null,handle:i=>[l("columns",i)]}),n("columns",()=>[{values:Array.from({length:12},(i,u)=>`${u+1}`),valueThemeKeys:["--columns","--container"]}]);for(let i of["auto","avoid","all","avoid-page","page","left","right","column"])e(`break-before-${i}`,[["break-before",i]]);for(let i of["auto","avoid","avoid-page","avoid-column"])e(`break-inside-${i}`,[["break-inside",i]]);for(let i of["auto","avoid","all","avoid-page","page","left","right","column"])e(`break-after-${i}`,[["break-after",i]]);e("grid-flow-row",[["grid-auto-flow","row"]]),e("grid-flow-col",[["grid-auto-flow","column"]]),e("grid-flow-dense",[["grid-auto-flow","dense"]]),e("grid-flow-row-dense",[["grid-auto-flow","row dense"]]),e("grid-flow-col-dense",[["grid-auto-flow","column dense"]]),e("auto-cols-auto",[["grid-auto-columns","auto"]]),e("auto-cols-min",[["grid-auto-columns","min-content"]]),e("auto-cols-max",[["grid-auto-columns","max-content"]]),e("auto-cols-fr",[["grid-auto-columns","minmax(0, 1fr)"]]),o("auto-cols",{themeKeys:["--grid-auto-columns"],handle:i=>[l("grid-auto-columns",i)]}),e("auto-rows-auto",[["grid-auto-rows","auto"]]),e("auto-rows-min",[["grid-auto-rows","min-content"]]),e("auto-rows-max",[["grid-auto-rows","max-content"]]),e("auto-rows-fr",[["grid-auto-rows","minmax(0, 1fr)"]]),o("auto-rows",{themeKeys:["--grid-auto-rows"],handle:i=>[l("grid-auto-rows",i)]}),e("grid-cols-none",[["grid-template-columns","none"]]),e("grid-cols-subgrid",[["grid-template-columns","subgrid"]]),o("grid-cols",{themeKeys:["--grid-template-columns"],handleBareValue:({value:i})=>T(i)?`repeat(${i}, minmax(0, 1fr))`:null,handle:i=>[l("grid-template-columns",i)]}),e("grid-rows-none",[["grid-template-rows","none"]]),e("grid-rows-subgrid",[["grid-template-rows","subgrid"]]),o("grid-rows",{themeKeys:["--grid-template-rows"],handleBareValue:({value:i})=>T(i)?`repeat(${i}, minmax(0, 1fr))`:null,handle:i=>[l("grid-template-rows",i)]}),n("grid-cols",()=>[{values:Array.from({length:12},(i,u)=>`${u+1}`),valueThemeKeys:["--grid-template-columns"]}]),n("grid-rows",()=>[{values:Array.from({length:12},(i,u)=>`${u+1}`),valueThemeKeys:["--grid-template-rows"]}]),e("flex-row",[["flex-direction","row"]]),e("flex-row-reverse",[["flex-direction","row-reverse"]]),e("flex-col",[["flex-direction","column"]]),e("flex-col-reverse",[["flex-direction","column-reverse"]]),e("flex-wrap",[["flex-wrap","wrap"]]),e("flex-nowrap",[["flex-wrap","nowrap"]]),e("flex-wrap-reverse",[["flex-wrap","wrap-reverse"]]),e("place-content-center",[["place-content","center"]]),e("place-content-start",[["place-content","start"]]),e("place-content-end",[["place-content","end"]]),e("place-content-between",[["place-content","space-between"]]),e("place-content-around",[["place-content","space-around"]]),e("place-content-evenly",[["place-content","space-evenly"]]),e("place-content-baseline",[["place-content","baseline"]]),e("place-content-stretch",[["place-content","stretch"]]),e("place-items-center",[["place-items","center"]]),e("place-items-start",[["place-items","start"]]),e("place-items-end",[["place-items","end"]]),e("place-items-baseline",[["place-items","baseline"]]),e("place-items-stretch",[["place-items","stretch"]]),e("content-normal",[["align-content","normal"]]),e("content-center",[["align-content","center"]]),e("content-start",[["align-content","flex-start"]]),e("content-end",[["align-content","flex-end"]]),e("content-between",[["align-content","space-between"]]),e("content-around",[["align-content","space-around"]]),e("content-evenly",[["align-content","space-evenly"]]),e("content-baseline",[["align-content","baseline"]]),e("content-stretch",[["align-content","stretch"]]),e("items-center",[["align-items","center"]]),e("items-start",[["align-items","flex-start"]]),e("items-end",[["align-items","flex-end"]]),e("items-baseline",[["align-items","baseline"]]),e("items-stretch",[["align-items","stretch"]]),e("justify-normal",[["justify-content","normal"]]),e("justify-center",[["justify-content","center"]]),e("justify-start",[["justify-content","flex-start"]]),e("justify-end",[["justify-content","flex-end"]]),e("justify-between",[["justify-content","space-between"]]),e("justify-around",[["justify-content","space-around"]]),e("justify-evenly",[["justify-content","space-evenly"]]),e("justify-baseline",[["justify-content","baseline"]]),e("justify-stretch",[["justify-content","stretch"]]),e("justify-items-normal",[["justify-items","normal"]]),e("justify-items-center",[["justify-items","center"]]),e("justify-items-start",[["justify-items","start"]]),e("justify-items-end",[["justify-items","end"]]),e("justify-items-stretch",[["justify-items","stretch"]]),a("gap",["--gap","--spacing"],i=>[l("gap",i)]),a("gap-x",["--gap","--spacing"],i=>[l("column-gap",i)]),a("gap-y",["--gap","--spacing"],i=>[l("row-gap",i)]),a("space-x",["--space","--spacing"],i=>[_([$("--tw-space-x-reverse","0")]),F(":where(& > :not(:last-child))",[l("--tw-sort","row-gap"),l("--tw-space-x-reverse","0"),l("margin-inline-start",`calc(${i} * var(--tw-space-x-reverse))`),l("margin-inline-end",`calc(${i} * calc(1 - var(--tw-space-x-reverse)))`)])],{supportsNegative:!0}),a("space-y",["--space","--spacing"],i=>[_([$("--tw-space-y-reverse","0")]),F(":where(& > :not(:last-child))",[l("--tw-sort","column-gap"),l("--tw-space-y-reverse","0"),l("margin-block-start",`calc(${i} * var(--tw-space-y-reverse))`),l("margin-block-end",`calc(${i} * calc(1 - var(--tw-space-y-reverse)))`)])],{supportsNegative:!0}),e("space-x-reverse",[()=>_([$("--tw-space-x-reverse","0")]),()=>F(":where(& > :not(:last-child))",[l("--tw-sort","row-gap"),l("--tw-space-x-reverse","1")])]),e("space-y-reverse",[()=>_([$("--tw-space-y-reverse","0")]),()=>F(":where(& > :not(:last-child))",[l("--tw-sort","column-gap"),l("--tw-space-y-reverse","1")])]),e("accent-auto",[["accent-color","auto"]]),s("accent",{themeKeys:["--accent-color","--color"],handle:i=>[l("accent-color",i)]}),s("caret",{themeKeys:["--caret-color","--color"],handle:i=>[l("caret-color",i)]}),s("divide",{themeKeys:["--divide-color","--color"],handle:i=>[F(":where(& > :not(:last-child))",[l("--tw-sort","divide-color"),l("border-color",i)])]}),e("place-self-auto",[["place-self","auto"]]),e("place-self-start",[["place-self","start"]]),e("place-self-end",[["place-self","end"]]),e("place-self-center",[["place-self","center"]]),e("place-self-stretch",[["place-self","stretch"]]),e("self-auto",[["align-self","auto"]]),e("self-start",[["align-self","flex-start"]]),e("self-end",[["align-self","flex-end"]]),e("self-center",[["align-self","center"]]),e("self-stretch",[["align-self","stretch"]]),e("self-baseline",[["align-self","baseline"]]),e("justify-self-auto",[["justify-self","auto"]]),e("justify-self-start",[["justify-self","flex-start"]]),e("justify-self-end",[["justify-self","flex-end"]]),e("justify-self-center",[["justify-self","center"]]),e("justify-self-stretch",[["justify-self","stretch"]]);for(let i of["auto","hidden","clip","visible","scroll"])e(`overflow-${i}`,[["overflow",i]]),e(`overflow-x-${i}`,[["overflow-x",i]]),e(`overflow-y-${i}`,[["overflow-y",i]]);for(let i of["auto","contain","none"])e(`overscroll-${i}`,[["overscroll-behavior",i]]),e(`overscroll-x-${i}`,[["overscroll-behavior-x",i]]),e(`overscroll-y-${i}`,[["overscroll-behavior-y",i]]);e("scroll-auto",[["scroll-behavior","auto"]]),e("scroll-smooth",[["scroll-behavior","smooth"]]),e("truncate",[["overflow","hidden"],["text-overflow","ellipsis"],["white-space","nowrap"]]),e("text-ellipsis",[["text-overflow","ellipsis"]]),e("text-clip",[["text-overflow","clip"]]),e("hyphens-none",[["-webkit-hyphens","none"],["hyphens","none"]]),e("hyphens-manual",[["-webkit-hyphens","manual"],["hyphens","manual"]]),e("hyphens-auto",[["-webkit-hyphens","auto"],["hyphens","auto"]]),e("whitespace-normal",[["white-space","normal"]]),e("whitespace-nowrap",[["white-space","nowrap"]]),e("whitespace-pre",[["white-space","pre"]]),e("whitespace-pre-line",[["white-space","pre-line"]]),e("whitespace-pre-wrap",[["white-space","pre-wrap"]]),e("whitespace-break-spaces",[["white-space","break-spaces"]]),e("text-wrap",[["text-wrap","wrap"]]),e("text-nowrap",[["text-wrap","nowrap"]]),e("text-balance",[["text-wrap","balance"]]),e("text-pretty",[["text-wrap","pretty"]]),e("break-normal",[["overflow-wrap","normal"],["word-break","normal"]]),e("break-words",[["overflow-wrap","break-word"]]),e("break-all",[["word-break","break-all"]]),e("break-keep",[["word-break","keep-all"]]);for(let[i,u]of[["rounded",["border-radius"]],["rounded-s",["border-start-start-radius","border-end-start-radius"]],["rounded-e",["border-start-end-radius","border-end-end-radius"]],["rounded-t",["border-top-left-radius","border-top-right-radius"]],["rounded-r",["border-top-right-radius","border-bottom-right-radius"]],["rounded-b",["border-bottom-right-radius","border-bottom-left-radius"]],["rounded-l",["border-top-left-radius","border-bottom-left-radius"]],["rounded-ss",["border-start-start-radius"]],["rounded-se",["border-start-end-radius"]],["rounded-ee",["border-end-end-radius"]],["rounded-es",["border-end-start-radius"]],["rounded-tl",["border-top-left-radius"]],["rounded-tr",["border-top-right-radius"]],["rounded-br",["border-bottom-right-radius"]],["rounded-bl",["border-bottom-left-radius"]]])e(`${i}-none`,u.map(m=>[m,"0"])),e(`${i}-full`,u.map(m=>[m,"calc(infinity * 1px)"])),o(i,{themeKeys:["--radius"],handle:m=>u.map(k=>l(k,m))});e("border-solid",[["--tw-border-style","solid"],["border-style","solid"]]),e("border-dashed",[["--tw-border-style","dashed"],["border-style","dashed"]]),e("border-dotted",[["--tw-border-style","dotted"],["border-style","dotted"]]),e("border-double",[["--tw-border-style","double"],["border-style","double"]]),e("border-hidden",[["--tw-border-style","hidden"],["border-style","hidden"]]),e("border-none",[["--tw-border-style","none"],["border-style","none"]]);{let u=function(m,k){r.functional(m,b=>{if(!b.value){if(b.modifier)return;let N=t.get(["--default-border-width"])??"1px",K=k.width(N);return K?[i(),...K]:void 0}if(b.value.kind==="arbitrary"){let N=b.value.value;switch(b.value.dataType??I(N,["color","line-width","length"])){case"line-width":case"length":{if(b.modifier)return;let C=k.width(N);return C?[i(),...C]:void 0}default:return N=W(N,b.modifier,t),N===null?void 0:k.color(N)}}{let N=q(b,t,["--border-color","--color"]);if(N)return k.color(N)}{if(b.modifier)return;let N=t.resolve(b.value.value,["--border-width"]);if(N){let K=k.width(N);return K?[i(),...K]:void 0}if(T(b.value.value)){let K=k.width(`${b.value.value}px`);return K?[i(),...K]:void 0}}}),n(m,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--border-color","--color"],modifiers:Array.from({length:21},(b,N)=>`${N*5}`),hasDefaultValue:!0},{values:["0","2","4","8"],valueThemeKeys:["--border-width"]}])};var x=u;let i=()=>_([$("--tw-border-style","solid")]);u("border",{width:m=>[l("border-style","var(--tw-border-style)"),l("border-width",m)],color:m=>[l("border-color",m)]}),u("border-x",{width:m=>[l("border-inline-style","var(--tw-border-style)"),l("border-inline-width",m)],color:m=>[l("border-inline-color",m)]}),u("border-y",{width:m=>[l("border-block-style","var(--tw-border-style)"),l("border-block-width",m)],color:m=>[l("border-block-color",m)]}),u("border-s",{width:m=>[l("border-inline-start-style","var(--tw-border-style)"),l("border-inline-start-width",m)],color:m=>[l("border-inline-start-color",m)]}),u("border-e",{width:m=>[l("border-inline-end-style","var(--tw-border-style)"),l("border-inline-end-width",m)],color:m=>[l("border-inline-end-color",m)]}),u("border-t",{width:m=>[l("border-top-style","var(--tw-border-style)"),l("border-top-width",m)],color:m=>[l("border-top-color",m)]}),u("border-r",{width:m=>[l("border-right-style","var(--tw-border-style)"),l("border-right-width",m)],color:m=>[l("border-right-color",m)]}),u("border-b",{width:m=>[l("border-bottom-style","var(--tw-border-style)"),l("border-bottom-width",m)],color:m=>[l("border-bottom-color",m)]}),u("border-l",{width:m=>[l("border-left-style","var(--tw-border-style)"),l("border-left-width",m)],color:m=>[l("border-left-color",m)]}),o("divide-x",{defaultValue:t.get(["--default-border-width"])??"1px",themeKeys:["--divide-width","--border-width"],handleBareValue:({value:m})=>T(m)?`${m}px`:null,handle:m=>[_([$("--tw-divide-x-reverse","0")]),F(":where(& > :not(:last-child))",[l("--tw-sort","divide-x-width"),i(),l("--tw-divide-x-reverse","0"),l("border-inline-style","var(--tw-border-style)"),l("border-inline-start-width",`calc(${m} * var(--tw-divide-x-reverse))`),l("border-inline-end-width",`calc(${m} * calc(1 - var(--tw-divide-x-reverse)))`)])]}),o("divide-y",{defaultValue:t.get(["--default-border-width"])??"1px",themeKeys:["--divide-width","--border-width"],handleBareValue:({value:m})=>T(m)?`${m}px`:null,handle:m=>[_([$("--tw-divide-y-reverse","0")]),F(":where(& > :not(:last-child))",[l("--tw-sort","divide-y-width"),i(),l("--tw-divide-y-reverse","0"),l("border-bottom-style","var(--tw-border-style)"),l("border-top-style","var(--tw-border-style)"),l("border-top-width",`calc(${m} * var(--tw-divide-y-reverse))`),l("border-bottom-width",`calc(${m} * calc(1 - var(--tw-divide-y-reverse)))`)])]}),n("divide-x",()=>[{values:["0","2","4","8"],valueThemeKeys:["--divide-width","--border-width"],hasDefaultValue:!0}]),n("divide-y",()=>[{values:["0","2","4","8"],valueThemeKeys:["--divide-width","--border-width"],hasDefaultValue:!0}]),e("divide-x-reverse",[()=>_([$("--tw-divide-x-reverse","0")]),()=>F(":where(& > :not(:last-child))",[l("--tw-divide-x-reverse","1")])]),e("divide-y-reverse",[()=>_([$("--tw-divide-y-reverse","0")]),()=>F(":where(& > :not(:last-child))",[l("--tw-divide-y-reverse","1")])]);for(let m of["solid","dashed","dotted","double","none"])e(`divide-${m}`,[()=>F(":where(& > :not(:last-child))",[l("--tw-sort","divide-style"),l("--tw-border-style",m),l("border-style",m)])])}e("bg-auto",[["background-size","auto"]]),e("bg-cover",[["background-size","cover"]]),e("bg-contain",[["background-size","contain"]]),e("bg-fixed",[["background-attachment","fixed"]]),e("bg-local",[["background-attachment","local"]]),e("bg-scroll",[["background-attachment","scroll"]]),e("bg-center",[["background-position","center"]]),e("bg-top",[["background-position","top"]]),e("bg-right-top",[["background-position","right top"]]),e("bg-right",[["background-position","right"]]),e("bg-right-bottom",[["background-position","right bottom"]]),e("bg-bottom",[["background-position","bottom"]]),e("bg-left-bottom",[["background-position","left bottom"]]),e("bg-left",[["background-position","left"]]),e("bg-left-top",[["background-position","left top"]]),e("bg-repeat",[["background-repeat","repeat"]]),e("bg-no-repeat",[["background-repeat","no-repeat"]]),e("bg-repeat-x",[["background-repeat","repeat-x"]]),e("bg-repeat-y",[["background-repeat","repeat-y"]]),e("bg-repeat-round",[["background-repeat","round"]]),e("bg-repeat-space",[["background-repeat","space"]]),e("bg-none",[["background-image","none"]]);{let m=function(N){let K="in oklab";if(N?.kind==="named")switch(N.value){case"longer":case"shorter":case"increasing":case"decreasing":K=`in oklch ${N.value} hue`;break;default:K=`in ${N.value}`}else N?.kind==="arbitrary"&&(K=N.value);return K},k=function({negative:N}){return K=>{if(!K.value)return;if(K.value.kind==="arbitrary"){if(K.modifier)return;let U=K.value.value;switch(K.value.dataType??I(U,["angle"])){case"angle":return U=N?`calc(${U} * -1)`:`${U}`,[l("--tw-gradient-position",`${U},`),l("background-image",`linear-gradient(var(--tw-gradient-stops,${U}))`)];default:return N?void 0:[l("--tw-gradient-position",`${U},`),l("background-image",`linear-gradient(var(--tw-gradient-stops,${U}))`)]}}let C=K.value.value;if(!N&&u.has(C))C=u.get(C);else if(T(C))C=N?`calc(${C}deg * -1)`:`${C}deg`;else return;let A=m(K.modifier);return[l("--tw-gradient-position",`${C} ${A},`),l("background-image","linear-gradient(var(--tw-gradient-stops))")]}},b=function({negative:N}){return K=>{if(K.value?.kind==="arbitrary"){if(K.modifier)return;let U=K.value.value;return[l("--tw-gradient-position",`${U},`),l("background-image",`conic-gradient(var(--tw-gradient-stops,${U}))`)]}let C=m(K.modifier);if(!K.value)return[l("--tw-gradient-position",`${C},`),l("background-image","conic-gradient(var(--tw-gradient-stops))")];let A=K.value.value;if(T(A))return A=N?`calc(${A} * -1)`:`${A}deg`,[l("--tw-gradient-position",`from ${A} ${C},`),l("background-image","conic-gradient(var(--tw-gradient-stops))")]}};var V=m,S=k,R=b;let i=["oklab","oklch","srgb","hsl","longer","shorter","increasing","decreasing"],u=new Map([["to-t","to top"],["to-tr","to top right"],["to-r","to right"],["to-br","to bottom right"],["to-b","to bottom"],["to-bl","to bottom left"],["to-l","to left"],["to-tl","to top left"]]);r.functional("-bg-linear",k({negative:!0})),r.functional("bg-linear",k({negative:!1})),n("bg-linear",()=>[{values:[...u.keys()],modifiers:i},{values:["0","30","60","90","120","150","180","210","240","270","300","330"],supportsNegative:!0,modifiers:i}]),r.functional("-bg-conic",b({negative:!0})),r.functional("bg-conic",b({negative:!1})),n("bg-conic",()=>[{hasDefaultValue:!0,modifiers:i},{values:["0","30","60","90","120","150","180","210","240","270","300","330"],supportsNegative:!0,modifiers:i}]),r.functional("bg-radial",N=>{if(!N.value){let K=m(N.modifier);return[l("--tw-gradient-position",`${K},`),l("background-image","radial-gradient(var(--tw-gradient-stops))")]}if(N.value.kind==="arbitrary"){if(N.modifier)return;let K=N.value.value;return[l("--tw-gradient-position",`${K},`),l("background-image",`radial-gradient(var(--tw-gradient-stops,${K}))`)]}}),n("bg-radial",()=>[{hasDefaultValue:!0,modifiers:i}])}r.functional("bg",i=>{if(i.value){if(i.value.kind==="arbitrary"){let u=i.value.value;switch(i.value.dataType??I(u,["image","color","percentage","position","bg-size","length","url"])){case"percentage":case"position":return i.modifier?void 0:[l("background-position",u)];case"bg-size":case"length":case"size":return i.modifier?void 0:[l("background-size",u)];case"image":case"url":return i.modifier?void 0:[l("background-image",u)];default:return u=W(u,i.modifier,t),u===null?void 0:[l("background-color",u)]}}{let u=q(i,t,["--background-color","--color"]);if(u)return[l("background-color",u)]}{if(i.modifier)return;let u=t.resolve(i.value.value,["--background-image"]);if(u)return[l("background-image",u)]}}}),n("bg",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(i,u)=>`${u*5}`)},{values:[],valueThemeKeys:["--background-image"]}]);let w=()=>_([$("--tw-gradient-position"),$("--tw-gradient-from","#0000","<color>"),$("--tw-gradient-via","#0000","<color>"),$("--tw-gradient-to","#0000","<color>"),$("--tw-gradient-stops"),$("--tw-gradient-via-stops"),$("--tw-gradient-from-position","0%","<length-percentage>"),$("--tw-gradient-via-position","50%","<length-percentage>"),$("--tw-gradient-to-position","100%","<length-percentage>")]);function v(i,u){r.functional(i,m=>{if(m.value){if(m.value.kind==="arbitrary"){let k=m.value.value;switch(m.value.dataType??I(k,["color","length","percentage"])){case"length":case"percentage":return m.modifier?void 0:u.position(k);default:return k=W(k,m.modifier,t),k===null?void 0:u.color(k)}}{let k=q(m,t,["--background-color","--color"]);if(k)return u.color(k)}{if(m.modifier)return;let k=t.resolve(m.value.value,["--gradient-color-stop-positions"]);if(k)return u.position(k);if(m.value.value[m.value.value.length-1]==="%"&&T(m.value.value.slice(0,-1)))return u.position(m.value.value)}}}),n(i,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(m,k)=>`${k*5}`)},{values:Array.from({length:21},(m,k)=>`${k*5}%`),valueThemeKeys:["--gradient-color-stop-positions"]}])}v("from",{color:i=>[w(),l("--tw-sort","--tw-gradient-from"),l("--tw-gradient-from",i),l("--tw-gradient-stops","var(--tw-gradient-via-stops, var(--tw-gradient-position,) var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))")],position:i=>[w(),l("--tw-gradient-from-position",i)]}),e("via-none",[["--tw-gradient-via-stops","initial"]]),v("via",{color:i=>[w(),l("--tw-sort","--tw-gradient-via"),l("--tw-gradient-via",i),l("--tw-gradient-via-stops","var(--tw-gradient-position,) var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position)"),l("--tw-gradient-stops","var(--tw-gradient-via-stops)")],position:i=>[w(),l("--tw-gradient-via-position",i)]}),v("to",{color:i=>[w(),l("--tw-sort","--tw-gradient-to"),l("--tw-gradient-to",i),l("--tw-gradient-stops","var(--tw-gradient-via-stops, var(--tw-gradient-position,) var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))")],position:i=>[w(),l("--tw-gradient-to-position",i)]}),e("box-decoration-slice",[["-webkit-box-decoration-break","slice"],["box-decoration-break","slice"]]),e("box-decoration-clone",[["-webkit-box-decoration-break","clone"],["box-decoration-break","clone"]]),e("bg-clip-text",[["background-clip","text"]]),e("bg-clip-border",[["background-clip","border-box"]]),e("bg-clip-padding",[["background-clip","padding-box"]]),e("bg-clip-content",[["background-clip","content-box"]]),e("bg-origin-border",[["background-origin","border-box"]]),e("bg-origin-padding",[["background-origin","padding-box"]]),e("bg-origin-content",[["background-origin","content-box"]]);for(let i of["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"])e(`bg-blend-${i}`,[["background-blend-mode",i]]),e(`mix-blend-${i}`,[["mix-blend-mode",i]]);e("mix-blend-plus-darker",[["mix-blend-mode","plus-darker"]]),e("mix-blend-plus-lighter",[["mix-blend-mode","plus-lighter"]]),e("fill-none",[["fill","none"]]),r.functional("fill",i=>{if(!i.value)return;if(i.value.kind==="arbitrary"){let m=W(i.value.value,i.modifier,t);return m===null?void 0:[l("fill",m)]}let u=q(i,t,["--fill","--color"]);if(u)return[l("fill",u)]}),n("fill",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--fill","--color"],modifiers:Array.from({length:21},(i,u)=>`${u*5}`)}]),e("stroke-none",[["stroke","none"]]),r.functional("stroke",i=>{if(i.value){if(i.value.kind==="arbitrary"){let u=i.value.value;switch(i.value.dataType??I(u,["color","number","length","percentage"])){case"number":case"length":case"percentage":return i.modifier?void 0:[l("stroke-width",u)];default:return u=W(i.value.value,i.modifier,t),u===null?void 0:[l("stroke",u)]}}{let u=q(i,t,["--stroke","--color"]);if(u)return[l("stroke",u)]}{let u=t.resolve(i.value.value,["--stroke-width"]);if(u)return[l("stroke-width",u)];if(T(i.value.value))return[l("stroke-width",i.value.value)]}}}),n("stroke",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--stroke","--color"],modifiers:Array.from({length:21},(i,u)=>`${u*5}`)},{values:["0","1","2","3"],valueThemeKeys:["--stroke-width"]}]),e("object-contain",[["object-fit","contain"]]),e("object-cover",[["object-fit","cover"]]),e("object-fill",[["object-fit","fill"]]),e("object-none",[["object-fit","none"]]),e("object-scale-down",[["object-fit","scale-down"]]),e("object-bottom",[["object-position","bottom"]]),e("object-center",[["object-position","center"]]),e("object-left",[["object-position","left"]]),e("object-left-bottom",[["object-position","left bottom"]]),e("object-left-top",[["object-position","left top"]]),e("object-right",[["object-position","right"]]),e("object-right-bottom",[["object-position","right bottom"]]),e("object-right-top",[["object-position","right top"]]),e("object-top",[["object-position","top"]]),o("object",{themeKeys:["--object-position"],handle:i=>[l("object-position",i)]});for(let[i,u]of[["p","padding"],["px","padding-inline"],["py","padding-block"],["ps","padding-inline-start"],["pe","padding-inline-end"],["pt","padding-top"],["pr","padding-right"],["pb","padding-bottom"],["pl","padding-left"]])a(i,["--padding","--spacing"],m=>[l(u,m)]);e("text-left",[["text-align","left"]]),e("text-center",[["text-align","center"]]),e("text-right",[["text-align","right"]]),e("text-justify",[["text-align","justify"]]),e("text-start",[["text-align","start"]]),e("text-end",[["text-align","end"]]),a("indent",["--text-indent","--spacing"],i=>[l("text-indent",i)],{supportsNegative:!0}),e("align-baseline",[["vertical-align","baseline"]]),e("align-top",[["vertical-align","top"]]),e("align-middle",[["vertical-align","middle"]]),e("align-bottom",[["vertical-align","bottom"]]),e("align-text-top",[["vertical-align","text-top"]]),e("align-text-bottom",[["vertical-align","text-bottom"]]),e("align-sub",[["vertical-align","sub"]]),e("align-super",[["vertical-align","super"]]),o("align",{themeKeys:[],handle:i=>[l("vertical-align",i)]}),r.functional("font",i=>{if(!(!i.value||i.modifier)){if(i.value.kind==="arbitrary"){let u=i.value.value;switch(i.value.dataType??I(u,["number","generic-name","family-name"])){case"generic-name":case"family-name":return[l("font-family",u)];default:return[_([$("--tw-font-weight")]),l("--tw-font-weight",u),l("font-weight",u)]}}{let u=t.resolveWith(i.value.value,["--font"],["--font-feature-settings","--font-variation-settings"]);if(u){let[m,k={}]=u;return[l("font-family",m),l("font-feature-settings",k["--font-feature-settings"]),l("font-variation-settings",k["--font-variation-settings"])]}}{let u=t.resolve(i.value.value,["--font-weight"]);if(u)return[_([$("--tw-font-weight")]),l("--tw-font-weight",u),l("font-weight",u)]}}}),n("font",()=>[{values:[],valueThemeKeys:["--font"]},{values:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],valueThemeKeys:["--font-weight"]}]),e("uppercase",[["text-transform","uppercase"]]),e("lowercase",[["text-transform","lowercase"]]),e("capitalize",[["text-transform","capitalize"]]),e("normal-case",[["text-transform","none"]]),e("italic",[["font-style","italic"]]),e("not-italic",[["font-style","normal"]]),e("underline",[["text-decoration-line","underline"]]),e("overline",[["text-decoration-line","overline"]]),e("line-through",[["text-decoration-line","line-through"]]),e("no-underline",[["text-decoration-line","none"]]),e("font-stretch-normal",[["font-stretch","normal"]]),e("font-stretch-ultra-condensed",[["font-stretch","ultra-condensed"]]),e("font-stretch-extra-condensed",[["font-stretch","extra-condensed"]]),e("font-stretch-condensed",[["font-stretch","condensed"]]),e("font-stretch-semi-condensed",[["font-stretch","semi-condensed"]]),e("font-stretch-semi-expanded",[["font-stretch","semi-expanded"]]),e("font-stretch-expanded",[["font-stretch","expanded"]]),e("font-stretch-extra-expanded",[["font-stretch","extra-expanded"]]),e("font-stretch-ultra-expanded",[["font-stretch","ultra-expanded"]]),o("font-stretch",{handleBareValue:({value:i})=>{if(!i.endsWith("%"))return null;let u=Number(i.slice(0,-1));return!T(u)||Number.isNaN(u)||u<50||u>200?null:i},handle:i=>[l("font-stretch",i)]}),n("font-stretch",()=>[{values:["50%","75%","90%","95%","100%","105%","110%","125%","150%","200%"]}]),s("placeholder",{themeKeys:["--background-color","--color"],handle:i=>[F("&::placeholder",[l("--tw-sort","placeholder-color"),l("color",i)])]}),e("decoration-solid",[["text-decoration-style","solid"]]),e("decoration-double",[["text-decoration-style","double"]]),e("decoration-dotted",[["text-decoration-style","dotted"]]),e("decoration-dashed",[["text-decoration-style","dashed"]]),e("decoration-wavy",[["text-decoration-style","wavy"]]),e("decoration-auto",[["text-decoration-thickness","auto"]]),e("decoration-from-font",[["text-decoration-thickness","from-font"]]),r.functional("decoration",i=>{if(i.value){if(i.value.kind==="arbitrary"){let u=i.value.value;switch(i.value.dataType??I(u,["color","length","percentage"])){case"length":case"percentage":return i.modifier?void 0:[l("text-decoration-thickness",u)];default:return u=W(u,i.modifier,t),u===null?void 0:[l("text-decoration-color",u)]}}{let u=t.resolve(i.value.value,["--text-decoration-thickness"]);if(u)return i.modifier?void 0:[l("text-decoration-thickness",u)];if(T(i.value.value))return i.modifier?void 0:[l("text-decoration-thickness",`${i.value.value}px`)]}{let u=q(i,t,["--text-decoration-color","--color"]);if(u)return[l("text-decoration-color",u)]}}}),n("decoration",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-decoration-color","--color"],modifiers:Array.from({length:21},(i,u)=>`${u*5}`)},{values:["0","1","2"],valueThemeKeys:["--text-decoration-thickness"]}]),e("animate-none",[["animation","none"]]),o("animate",{themeKeys:["--animate"],handle:i=>[l("animation",i)]});{let i=["var(--tw-blur,)","var(--tw-brightness,)","var(--tw-contrast,)","var(--tw-grayscale,)","var(--tw-hue-rotate,)","var(--tw-invert,)","var(--tw-saturate,)","var(--tw-sepia,)","var(--tw-drop-shadow,)"].join(" "),u=["var(--tw-backdrop-blur,)","var(--tw-backdrop-brightness,)","var(--tw-backdrop-contrast,)","var(--tw-backdrop-grayscale,)","var(--tw-backdrop-hue-rotate,)","var(--tw-backdrop-invert,)","var(--tw-backdrop-opacity,)","var(--tw-backdrop-saturate,)","var(--tw-backdrop-sepia,)"].join(" "),m=()=>_([$("--tw-blur"),$("--tw-brightness"),$("--tw-contrast"),$("--tw-grayscale"),$("--tw-hue-rotate"),$("--tw-invert"),$("--tw-opacity"),$("--tw-saturate"),$("--tw-sepia")]),k=()=>_([$("--tw-backdrop-blur"),$("--tw-backdrop-brightness"),$("--tw-backdrop-contrast"),$("--tw-backdrop-grayscale"),$("--tw-backdrop-hue-rotate"),$("--tw-backdrop-invert"),$("--tw-backdrop-opacity"),$("--tw-backdrop-saturate"),$("--tw-backdrop-sepia")]);r.functional("filter",b=>{if(!b.modifier){if(b.value===null)return[m(),l("filter",i)];if(b.value.kind==="arbitrary")return[l("filter",b.value.value)];switch(b.value.value){case"none":return[l("filter","none")]}}}),r.functional("backdrop-filter",b=>{if(!b.modifier){if(b.value===null)return[k(),l("-webkit-backdrop-filter",u),l("backdrop-filter",u)];if(b.value.kind==="arbitrary")return[l("-webkit-backdrop-filter",b.value.value),l("backdrop-filter",b.value.value)];switch(b.value.value){case"none":return[l("-webkit-backdrop-filter","none"),l("backdrop-filter","none")]}}}),o("blur",{themeKeys:["--blur"],handle:b=>[m(),l("--tw-blur",`blur(${b})`),l("filter",i)]}),e("blur-none",[m,["--tw-blur"," "],["filter",i]]),o("backdrop-blur",{themeKeys:["--backdrop-blur","--blur"],handle:b=>[k(),l("--tw-backdrop-blur",`blur(${b})`),l("-webkit-backdrop-filter",u),l("backdrop-filter",u)]}),e("backdrop-blur-none",[k,["--tw-backdrop-blur"," "],["-webkit-backdrop-filter",u],["backdrop-filter",u]]),o("brightness",{themeKeys:["--brightness"],handleBareValue:({value:b})=>T(b)?`${b}%`:null,handle:b=>[m(),l("--tw-brightness",`brightness(${b})`),l("filter",i)]}),o("backdrop-brightness",{themeKeys:["--backdrop-brightness","--brightness"],handleBareValue:({value:b})=>T(b)?`${b}%`:null,handle:b=>[k(),l("--tw-backdrop-brightness",`brightness(${b})`),l("-webkit-backdrop-filter",u),l("backdrop-filter",u)]}),n("brightness",()=>[{values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--brightness"]}]),n("backdrop-brightness",()=>[{values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--backdrop-brightness","--brightness"]}]),o("contrast",{themeKeys:["--contrast"],handleBareValue:({value:b})=>T(b)?`${b}%`:null,handle:b=>[m(),l("--tw-contrast",`contrast(${b})`),l("filter",i)]}),o("backdrop-contrast",{themeKeys:["--backdrop-contrast","--contrast"],handleBareValue:({value:b})=>T(b)?`${b}%`:null,handle:b=>[k(),l("--tw-backdrop-contrast",`contrast(${b})`),l("-webkit-backdrop-filter",u),l("backdrop-filter",u)]}),n("contrast",()=>[{values:["0","50","75","100","125","150","200"],valueThemeKeys:["--contrast"]}]),n("backdrop-contrast",()=>[{values:["0","50","75","100","125","150","200"],valueThemeKeys:["--backdrop-contrast","--contrast"]}]),o("grayscale",{themeKeys:["--grayscale"],handleBareValue:({value:b})=>T(b)?`${b}%`:null,defaultValue:"100%",handle:b=>[m(),l("--tw-grayscale",`grayscale(${b})`),l("filter",i)]}),o("backdrop-grayscale",{themeKeys:["--backdrop-grayscale","--grayscale"],handleBareValue:({value:b})=>T(b)?`${b}%`:null,defaultValue:"100%",handle:b=>[k(),l("--tw-backdrop-grayscale",`grayscale(${b})`),l("-webkit-backdrop-filter",u),l("backdrop-filter",u)]}),n("grayscale",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--grayscale"],hasDefaultValue:!0}]),n("backdrop-grayscale",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--backdrop-grayscale","--grayscale"],hasDefaultValue:!0}]),o("hue-rotate",{supportsNegative:!0,themeKeys:["--hue-rotate"],handleBareValue:({value:b})=>T(b)?`${b}deg`:null,handle:b=>[m(),l("--tw-hue-rotate",`hue-rotate(${b})`),l("filter",i)]}),o("backdrop-hue-rotate",{supportsNegative:!0,themeKeys:["--backdrop-hue-rotate","--hue-rotate"],handleBareValue:({value:b})=>T(b)?`${b}deg`:null,handle:b=>[k(),l("--tw-backdrop-hue-rotate",`hue-rotate(${b})`),l("-webkit-backdrop-filter",u),l("backdrop-filter",u)]}),n("hue-rotate",()=>[{values:["0","15","30","60","90","180"],valueThemeKeys:["--hue-rotate"]}]),n("backdrop-hue-rotate",()=>[{values:["0","15","30","60","90","180"],valueThemeKeys:["--backdrop-hue-rotate","--hue-rotate"]}]),o("invert",{themeKeys:["--invert"],handleBareValue:({value:b})=>T(b)?`${b}%`:null,defaultValue:"100%",handle:b=>[m(),l("--tw-invert",`invert(${b})`),l("filter",i)]}),o("backdrop-invert",{themeKeys:["--backdrop-invert","--invert"],handleBareValue:({value:b})=>T(b)?`${b}%`:null,defaultValue:"100%",handle:b=>[k(),l("--tw-backdrop-invert",`invert(${b})`),l("-webkit-backdrop-filter",u),l("backdrop-filter",u)]}),n("invert",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--invert"],hasDefaultValue:!0}]),n("backdrop-invert",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--backdrop-invert","--invert"],hasDefaultValue:!0}]),o("saturate",{themeKeys:["--saturate"],handleBareValue:({value:b})=>T(b)?`${b}%`:null,handle:b=>[m(),l("--tw-saturate",`saturate(${b})`),l("filter",i)]}),o("backdrop-saturate",{themeKeys:["--backdrop-saturate","--saturate"],handleBareValue:({value:b})=>T(b)?`${b}%`:null,handle:b=>[k(),l("--tw-backdrop-saturate",`saturate(${b})`),l("-webkit-backdrop-filter",u),l("backdrop-filter",u)]}),n("saturate",()=>[{values:["0","50","100","150","200"],valueThemeKeys:["--saturate"]}]),n("backdrop-saturate",()=>[{values:["0","50","100","150","200"],valueThemeKeys:["--backdrop-saturate","--saturate"]}]),o("sepia",{themeKeys:["--sepia"],handleBareValue:({value:b})=>T(b)?`${b}%`:null,defaultValue:"100%",handle:b=>[m(),l("--tw-sepia",`sepia(${b})`),l("filter",i)]}),o("backdrop-sepia",{themeKeys:["--backdrop-sepia","--sepia"],handleBareValue:({value:b})=>T(b)?`${b}%`:null,defaultValue:"100%",handle:b=>[k(),l("--tw-backdrop-sepia",`sepia(${b})`),l("-webkit-backdrop-filter",u),l("backdrop-filter",u)]}),n("sepia",()=>[{values:["0","50","100"],valueThemeKeys:["--sepia"],hasDefaultValue:!0}]),n("backdrop-sepia",()=>[{values:["0","50","100"],valueThemeKeys:["--backdrop-sepia","--sepia"],hasDefaultValue:!0}]),e("drop-shadow-none",[m,["--tw-drop-shadow"," "],["filter",i]]),o("drop-shadow",{themeKeys:["--drop-shadow"],handle:b=>[m(),l("--tw-drop-shadow",D(b,",").map(N=>`drop-shadow(${N})`).join(" ")),l("filter",i)]}),o("backdrop-opacity",{themeKeys:["--backdrop-opacity","--opacity"],handleBareValue:({value:b})=>ke(b)?`${b}%`:null,handle:b=>[k(),l("--tw-backdrop-opacity",`opacity(${b})`),l("-webkit-backdrop-filter",u),l("backdrop-filter",u)]}),n("backdrop-opacity",()=>[{values:Array.from({length:21},(b,N)=>`${N*5}`),valueThemeKeys:["--backdrop-opacity","--opacity"]}])}{let i=`var(--tw-ease, ${t.resolve(null,["--default-transition-timing-function"])??"ease"})`,u=`var(--tw-duration, ${t.resolve(null,["--default-transition-duration"])??"0s"})`;e("transition-none",[["transition-property","none"]]),e("transition-all",[["transition-property","all"],["transition-timing-function",i],["transition-duration",u]]),e("transition-colors",[["transition-property","color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to"],["transition-timing-function",i],["transition-duration",u]]),e("transition-opacity",[["transition-property","opacity"],["transition-timing-function",i],["transition-duration",u]]),e("transition-shadow",[["transition-property","box-shadow"],["transition-timing-function",i],["transition-duration",u]]),e("transition-transform",[["transition-property","transform, translate, scale, rotate"],["transition-timing-function",i],["transition-duration",u]]),o("transition",{defaultValue:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter",themeKeys:["--transition-property"],handle:m=>[l("transition-property",m),l("transition-timing-function",i),l("transition-duration",u)]}),e("transition-discrete",[["transition-behavior","allow-discrete"]]),e("transition-normal",[["transition-behavior","normal"]]),o("delay",{handleBareValue:({value:m})=>T(m)?`${m}ms`:null,themeKeys:["--transition-delay"],handle:m=>[l("transition-delay",m)]});{let m=()=>_([$("--tw-duration")]);e("duration-initial",[m,["--tw-duration","initial"]]),r.functional("duration",k=>{if(k.modifier||!k.value)return;let b=null;if(k.value.kind==="arbitrary"?b=k.value.value:(b=t.resolve(k.value.fraction??k.value.value,["--transition-duration"]),b===null&&T(k.value.value)&&(b=`${k.value.value}ms`)),b!==null)return[m(),l("--tw-duration",b),l("transition-duration",b)]})}n("delay",()=>[{values:["75","100","150","200","300","500","700","1000"],valueThemeKeys:["--transition-delay"]}]),n("duration",()=>[{values:["75","100","150","200","300","500","700","1000"],valueThemeKeys:["--transition-duration"]}])}{let i=()=>_([$("--tw-ease")]);e("ease-initial",[i,["--tw-ease","initial"]]),e("ease-linear",[i,["--tw-ease","linear"],["transition-timing-function","linear"]]),o("ease",{themeKeys:["--ease"],handle:u=>[i(),l("--tw-ease",u),l("transition-timing-function",u)]})}e("will-change-auto",[["will-change","auto"]]),e("will-change-scroll",[["will-change","scroll-position"]]),e("will-change-contents",[["will-change","contents"]]),e("will-change-transform",[["will-change","transform"]]),o("will-change",{themeKeys:[],handle:i=>[l("will-change",i)]}),e("content-none",[["--tw-content","none"],["content","none"]]),o("content",{themeKeys:[],handle:i=>[_([$("--tw-content",'""')]),l("--tw-content",i),l("content","var(--tw-content)")]});{let i="var(--tw-contain-size,) var(--tw-contain-layout,) var(--tw-contain-paint,) var(--tw-contain-style,)",u=()=>_([$("--tw-contain-size"),$("--tw-contain-layout"),$("--tw-contain-paint"),$("--tw-contain-style")]);e("contain-none",[["contain","none"]]),e("contain-content",[["contain","content"]]),e("contain-strict",[["contain","strict"]]),e("contain-size",[u,["--tw-contain-size","size"],["contain",i]]),e("contain-inline-size",[u,["--tw-contain-size","inline-size"],["contain",i]]),e("contain-layout",[u,["--tw-contain-layout","layout"],["contain",i]]),e("contain-paint",[u,["--tw-contain-paint","paint"],["contain",i]]),e("contain-style",[u,["--tw-contain-style","style"],["contain",i]]),o("contain",{themeKeys:[],handle:m=>[l("contain",m)]})}e("forced-color-adjust-none",[["forced-color-adjust","none"]]),e("forced-color-adjust-auto",[["forced-color-adjust","auto"]]),e("leading-none",[()=>_([$("--tw-leading")]),["--tw-leading","1"],["line-height","1"]]),a("leading",["--leading","--spacing"],i=>[_([$("--tw-leading")]),l("--tw-leading",i),l("line-height",i)]),o("tracking",{supportsNegative:!0,themeKeys:["--tracking"],handle:i=>[_([$("--tw-tracking")]),l("--tw-tracking",i),l("letter-spacing",i)]}),e("antialiased",[["-webkit-font-smoothing","antialiased"],["-moz-osx-font-smoothing","grayscale"]]),e("subpixel-antialiased",[["-webkit-font-smoothing","auto"],["-moz-osx-font-smoothing","auto"]]);{let i="var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,)",u=()=>_([$("--tw-ordinal"),$("--tw-slashed-zero"),$("--tw-numeric-figure"),$("--tw-numeric-spacing"),$("--tw-numeric-fraction")]);e("normal-nums",[["font-variant-numeric","normal"]]),e("ordinal",[u,["--tw-ordinal","ordinal"],["font-variant-numeric",i]]),e("slashed-zero",[u,["--tw-slashed-zero","slashed-zero"],["font-variant-numeric",i]]),e("lining-nums",[u,["--tw-numeric-figure","lining-nums"],["font-variant-numeric",i]]),e("oldstyle-nums",[u,["--tw-numeric-figure","oldstyle-nums"],["font-variant-numeric",i]]),e("proportional-nums",[u,["--tw-numeric-spacing","proportional-nums"],["font-variant-numeric",i]]),e("tabular-nums",[u,["--tw-numeric-spacing","tabular-nums"],["font-variant-numeric",i]]),e("diagonal-fractions",[u,["--tw-numeric-fraction","diagonal-fractions"],["font-variant-numeric",i]]),e("stacked-fractions",[u,["--tw-numeric-fraction","stacked-fractions"],["font-variant-numeric",i]])}{let i=()=>_([$("--tw-outline-style","solid")]);r.static("outline-hidden",()=>[l("outline-style","none"),O("@media","(forced-colors: active)",[l("outline","2px solid transparent"),l("outline-offset","2px")])]),e("outline-none",[["--tw-outline-style","none"],["outline-style","none"]]),e("outline-solid",[["--tw-outline-style","solid"],["outline-style","solid"]]),e("outline-dashed",[["--tw-outline-style","dashed"],["outline-style","dashed"]]),e("outline-dotted",[["--tw-outline-style","dotted"],["outline-style","dotted"]]),e("outline-double",[["--tw-outline-style","double"],["outline-style","double"]]),r.functional("outline",u=>{if(u.value===null)return u.modifier?void 0:[i(),l("outline-style","var(--tw-outline-style)"),l("outline-width","1px")];if(u.value.kind==="arbitrary"){let m=u.value.value;switch(u.value.dataType??I(m,["color","length","number","percentage"])){case"length":case"number":case"percentage":return u.modifier?void 0:[i(),l("outline-style","var(--tw-outline-style)"),l("outline-width",m)];default:return m=W(m,u.modifier,t),m===null?void 0:[l("outline-color",m)]}}{let m=q(u,t,["--outline-color","--color"]);if(m)return[l("outline-color",m)]}{if(u.modifier)return;let m=t.resolve(u.value.value,["--outline-width"]);if(m)return[i(),l("outline-style","var(--tw-outline-style)"),l("outline-width",m)];if(T(u.value.value))return[i(),l("outline-style","var(--tw-outline-style)"),l("outline-width",`${u.value.value}px`)]}}),n("outline",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--outline-color","--color"],modifiers:Array.from({length:21},(u,m)=>`${m*5}`),hasDefaultValue:!0},{values:["0","1","2","4","8"],valueThemeKeys:["--outline-width"]}]),o("outline-offset",{supportsNegative:!0,themeKeys:["--outline-offset"],handleBareValue:({value:u})=>T(u)?`${u}px`:null,handle:u=>[l("outline-offset",u)]}),n("outline-offset",()=>[{supportsNegative:!0,values:["0","1","2","4","8"],valueThemeKeys:["--outline-offset"]}])}o("opacity",{themeKeys:["--opacity"],handleBareValue:({value:i})=>ke(i)?`${i}%`:null,handle:i=>[l("opacity",i)]}),n("opacity",()=>[{values:Array.from({length:21},(i,u)=>`${u*5}`),valueThemeKeys:["--opacity"]}]),e("underline-offset-auto",[["text-underline-offset","auto"]]),o("underline-offset",{supportsNegative:!0,themeKeys:["--text-underline-offset"],handleBareValue:({value:i})=>T(i)?`${i}px`:null,handle:i=>[l("text-underline-offset",i)]}),n("underline-offset",()=>[{supportsNegative:!0,values:["0","1","2","4","8"],valueThemeKeys:["--text-underline-offset"]}]),r.functional("text",i=>{if(i.value){if(i.value.kind==="arbitrary"){let u=i.value.value;switch(i.value.dataType??I(u,["color","length","percentage","absolute-size","relative-size"])){case"size":case"length":case"percentage":case"absolute-size":case"relative-size":{if(i.modifier){let k=i.modifier.kind==="arbitrary"?i.modifier.value:t.resolve(i.modifier.value,["--leading"]);if(!k&&oe(i.modifier.value)){let b=t.resolve(null,["--spacing"]);if(!b)return null;k=`calc(${b} * ${i.modifier.value})`}if(k)return[l("font-size",u),l("line-height",k)]}return[l("font-size",u)]}default:return u=W(u,i.modifier,t),u===null?void 0:[l("color",u)]}}{let u=q(i,t,["--text-color","--color"]);if(u)return[l("color",u)]}{let u=t.resolveWith(i.value.value,["--text"],["--line-height","--letter-spacing","--font-weight"]);if(u){let[m,k={}]=Array.isArray(u)?u:[u];if(i.modifier){let b=i.modifier.kind==="arbitrary"?i.modifier.value:t.resolve(i.modifier.value,["--leading"]);if(!b&&oe(i.modifier.value)){let K=t.resolve(null,["--spacing"]);if(!K)return null;b=`calc(${K} * ${i.modifier.value})`}let N=[l("font-size",m)];return b&&N.push(l("line-height",b)),N}return typeof k=="string"?[l("font-size",m),l("line-height",k)]:[l("font-size",m),l("line-height",k["--line-height"]?`var(--tw-leading, ${k["--line-height"]})`:void 0),l("letter-spacing",k["--letter-spacing"]?`var(--tw-tracking, ${k["--letter-spacing"]})`:void 0),l("font-weight",k["--font-weight"]?`var(--tw-font-weight, ${k["--font-weight"]})`:void 0)]}}}}),n("text",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-color","--color"],modifiers:Array.from({length:21},(i,u)=>`${u*5}`)},{values:[],valueThemeKeys:["--text"],modifiers:[],modifierThemeKeys:["--leading"]}]);{let b=function(C){return`var(--tw-ring-inset,) 0 0 0 calc(${C} + var(--tw-ring-offset-width)) var(--tw-ring-color, ${k})`},N=function(C){return`inset 0 0 0 ${C} var(--tw-inset-ring-color, currentColor)`};var E=b,P=N;let i=["var(--tw-inset-shadow)","var(--tw-inset-ring-shadow)","var(--tw-ring-offset-shadow)","var(--tw-ring-shadow)","var(--tw-shadow)"].join(", "),u="0 0 #0000",m=()=>_([$("--tw-shadow",u),$("--tw-shadow-color"),$("--tw-inset-shadow",u),$("--tw-inset-shadow-color"),$("--tw-ring-color"),$("--tw-ring-shadow",u),$("--tw-inset-ring-color"),$("--tw-inset-ring-shadow",u),$("--tw-ring-inset"),$("--tw-ring-offset-width","0px","<length>"),$("--tw-ring-offset-color","#fff"),$("--tw-ring-offset-shadow",u)]);e("shadow-initial",[m,["--tw-shadow-color","initial"]]),r.functional("shadow",C=>{if(!C.value){let A=t.get(["--shadow"]);return A===null?void 0:[m(),l("--tw-shadow",ne(A,U=>`var(--tw-shadow-color, ${U})`)),l("box-shadow",i)]}if(C.value.kind==="arbitrary"){let A=C.value.value;switch(C.value.dataType??I(A,["color"])){case"color":return A=W(A,C.modifier,t),A===null?void 0:[m(),l("--tw-shadow-color",A)];default:return[m(),l("--tw-shadow",ne(A,be=>`var(--tw-shadow-color, ${be})`)),l("box-shadow",i)]}}switch(C.value.value){case"none":return C.modifier?void 0:[m(),l("--tw-shadow",u),l("box-shadow",i)]}{let A=t.get([`--shadow-${C.value.value}`]);if(A)return C.modifier?void 0:[m(),l("--tw-shadow",ne(A,U=>`var(--tw-shadow-color, ${U})`)),l("box-shadow",i)]}{let A=q(C,t,["--box-shadow-color","--color"]);if(A)return[m(),l("--tw-shadow-color",A)]}}),n("shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--box-shadow-color","--color"],modifiers:Array.from({length:21},(C,A)=>`${A*5}`)},{values:["none"],valueThemeKeys:["--shadow"],hasDefaultValue:!0}]),e("inset-shadow-initial",[m,["--tw-inset-shadow-color","initial"]]),r.functional("inset-shadow",C=>{if(!C.value){let A=t.get(["--inset-shadow"]);return A===null?void 0:[m(),l("--tw-inset-shadow",ne(A,U=>`var(--tw-inset-shadow-color, ${U})`)),l("box-shadow",i)]}if(C.value.kind==="arbitrary"){let A=C.value.value;switch(C.value.dataType??I(A,["color"])){case"color":return A=W(A,C.modifier,t),A===null?void 0:[m(),l("--tw-inset-shadow-color",A)];default:return[m(),l("--tw-inset-shadow",`inset ${ne(A,be=>`var(--tw-inset-shadow-color, ${be})`)}`),l("box-shadow",i)]}}switch(C.value.value){case"none":return C.modifier?void 0:[m(),l("--tw-inset-shadow",u),l("box-shadow",i)]}{let A=t.get([`--inset-shadow-${C.value.value}`]);if(A)return C.modifier?void 0:[m(),l("--tw-inset-shadow",ne(A,U=>`var(--tw-inset-shadow-color, ${U})`)),l("box-shadow",i)]}{let A=q(C,t,["--box-shadow-color","--color"]);if(A)return[m(),l("--tw-inset-shadow-color",A)]}}),n("inset-shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--box-shadow-color","--color"],modifiers:Array.from({length:21},(C,A)=>`${A*5}`)},{values:[],valueThemeKeys:["--inset-shadow"],hasDefaultValue:!0}]),e("ring-inset",[m,["--tw-ring-inset","inset"]]);let k=t.get(["--default-ring-color"])??"currentColor";r.functional("ring",C=>{if(!C.value){if(C.modifier)return;let A=t.get(["--default-ring-width"])??"1px";return[m(),l("--tw-ring-shadow",b(A)),l("box-shadow",i)]}if(C.value.kind==="arbitrary"){let A=C.value.value;switch(C.value.dataType??I(A,["color","length"])){case"length":return C.modifier?void 0:[m(),l("--tw-ring-shadow",b(A)),l("box-shadow",i)];default:return A=W(A,C.modifier,t),A===null?void 0:[l("--tw-ring-color",A)]}}{let A=q(C,t,["--ring-color","--color"]);if(A)return[l("--tw-ring-color",A)]}{if(C.modifier)return;let A=t.resolve(C.value.value,["--ring-width"]);if(A===null&&T(C.value.value)&&(A=`${C.value.value}px`),A)return[m(),l("--tw-ring-shadow",b(A)),l("box-shadow",i)]}}),n("ring",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-color","--color"],modifiers:Array.from({length:21},(C,A)=>`${A*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-width"],hasDefaultValue:!0}]),r.functional("inset-ring",C=>{if(!C.value)return C.modifier?void 0:[m(),l("--tw-inset-ring-shadow",N("1px")),l("box-shadow",i)];if(C.value.kind==="arbitrary"){let A=C.value.value;switch(C.value.dataType??I(A,["color","length"])){case"length":return C.modifier?void 0:[m(),l("--tw-inset-ring-shadow",N(A)),l("box-shadow",i)];default:return A=W(A,C.modifier,t),A===null?void 0:[l("--tw-inset-ring-color",A)]}}{let A=q(C,t,["--ring-color","--color"]);if(A)return[l("--tw-inset-ring-color",A)]}{if(C.modifier)return;let A=t.resolve(C.value.value,["--ring-width"]);if(A===null&&T(C.value.value)&&(A=`${C.value.value}px`),A)return[m(),l("--tw-inset-ring-shadow",N(A)),l("box-shadow",i)]}}),n("inset-ring",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-color","--color"],modifiers:Array.from({length:21},(C,A)=>`${A*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-width"],hasDefaultValue:!0}]);let K="var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color)";r.functional("ring-offset",C=>{if(C.value){if(C.value.kind==="arbitrary"){let A=C.value.value;switch(C.value.dataType??I(A,["color","length"])){case"length":return C.modifier?void 0:[l("--tw-ring-offset-width",A),l("--tw-ring-offset-shadow",K)];default:return A=W(A,C.modifier,t),A===null?void 0:[l("--tw-ring-offset-color",A)]}}{let A=t.resolve(C.value.value,["--ring-offset-width"]);if(A)return C.modifier?void 0:[l("--tw-ring-offset-width",A),l("--tw-ring-offset-shadow",K)];if(T(C.value.value))return C.modifier?void 0:[l("--tw-ring-offset-width",`${C.value.value}px`),l("--tw-ring-offset-shadow",K)]}{let A=q(C,t,["--ring-offset-color","--color"]);if(A)return[l("--tw-ring-offset-color",A)]}}})}return n("ring-offset",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-offset-color","--color"],modifiers:Array.from({length:21},(i,u)=>`${u*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-offset-width"]}]),r.functional("@container",i=>{let u=null;if(i.value===null?u="inline-size":i.value.kind==="arbitrary"?u=i.value.value:i.value.kind==="named"&&i.value.value==="normal"&&(u="normal"),u!==null)return i.modifier?[l("container-type",u),l("container-name",i.modifier.value)]:[l("container-type",u)]}),n("@container",()=>[{values:["normal"],valueThemeKeys:[],hasDefaultValue:!0}]),r}function Nt(t){let r=t.params;return jr.test(r)?n=>{let e=new Set,o=new Set;j(t.nodes,s=>{if(s.kind!=="declaration"||!s.value||!s.value.includes("--value(")&&!s.value.includes("--modifier("))return;let a=L(s.value);le(a,c=>{if(c.kind!=="function"||c.value!=="--value"&&c.value!=="--modifier")return;let d=D(B(c.nodes),",");for(let[f,p]of d.entries())p=p.replace(/\\\*/g,"*"),p=p.replace(/--(.*?)\s--(.*?)/g,"--$1-*--$2"),p=p.replace(/\s+/g,""),p=p.replace(/(-\*){2,}/g,"-*"),p[0]==="-"&&p[1]==="-"&&!p.includes("-*")&&(p+="-*"),d[f]=p;c.nodes=L(d.join(","));for(let f of c.nodes)if(f.kind==="word"&&f.value[0]==="-"&&f.value[1]==="-"){let p=f.value.replace(/-\*.*$/g,"");c.value==="--value"?e.add(p):c.value==="--modifier"&&o.add(p)}}),s.value=B(a)}),n.utilities.functional(r.slice(0,-2),s=>{let a=structuredClone(t),c=s.value,d=s.modifier;if(c===null)return;let f=!1,p=!1,g=!1,h=!1,y=new Map,w=!1;if(j([a],(v,{parent:x,replaceWith:V})=>{if(x?.kind!=="rule"&&x?.kind!=="at-rule"||v.kind!=="declaration"||!v.value)return;let S=L(v.value);(le(S,(E,{replaceWith:P})=>{if(E.kind==="function"){if(E.value==="--value"){f=!0;let i=$t(c,E,n);return i?(p=!0,i.ratio?w=!0:y.set(v,x),P(i.nodes),1):(f||=!1,V([]),2)}else if(E.value==="--modifier"){if(d===null)return V([]),1;g=!0;let i=$t(d,E,n);return i?(h=!0,P(i.nodes),1):(g||=!1,V([]),2)}}})??0)===0&&(v.value=B(S))}),f&&!p||g&&!h||w&&h||d&&!w&&!h)return null;if(w)for(let[v,x]of y){let V=x.nodes.indexOf(v);V!==-1&&x.nodes.splice(V,1)}return a.nodes}),n.utilities.suggest(r.slice(0,-2),()=>[{values:n.theme.keysInNamespaces(e).map(s=>s.replaceAll("_",".")),modifiers:n.theme.keysInNamespaces(o).map(s=>s.replaceAll("_","."))}])}:Or.test(r)?n=>{n.utilities.static(r,()=>structuredClone(t.nodes))}:null}function $t(t,r,n){for(let e of r.nodes)if(t.kind==="named"&&e.kind==="word"&&e.value[0]==="-"&&e.value[1]==="-"){let o=e.value;if(o.endsWith("-*")){o=o.slice(0,-2);let s=n.theme.resolve(t.value,[o]);if(s)return{nodes:L(s)}}else{let s=o.split("-*");if(s.length<=1)continue;let a=[s.shift()],c=n.theme.resolveWith(t.value,a,s);if(c){let[,d={}]=c;{let f=d[s.pop()];if(f)return{nodes:L(f)}}}}}else if(t.kind==="named"&&e.kind==="word"){if(e.value!=="number"&&e.value!=="integer"&&e.value!=="ratio"&&e.value!=="percentage")continue;let o=e.value==="ratio"&&"fraction"in t?t.fraction:t.value;if(!o)continue;let s=I(o,[e.value]);if(s===null)continue;if(s==="ratio"){let[a,c]=D(o,"/");if(!T(a)||!T(c))continue}else{if(s==="number"&&!oe(o))continue;if(s==="percentage"&&!T(o.slice(0,-1)))continue}return{nodes:L(o),ratio:s==="ratio"}}else if(t.kind==="arbitrary"&&e.kind==="word"&&e.value[0]==="["&&e.value[e.value.length-1]==="]"){let o=e.value.slice(1,-1);if(o==="*")return{nodes:L(t.value)};if("dataType"in t&&t.dataType&&t.dataType!==o)continue;if("dataType"in t&&t.dataType)return{nodes:L(t.value)};if(I(t.value,[o])!==null)return{nodes:L(t.value)}}}var Be={"--alpha":Dr,"--spacing":_r,"--theme":Fr,theme:Et};function Dr(t,r,...n){let[e,o]=D(r,"/").map(s=>s.trim());if(!e||!o)throw new Error(`The --alpha(\u2026) function requires a color and an alpha value, e.g.: \`--alpha(${e||"var(--my-color)"} / ${o||"50%"})\``);if(n.length>0)throw new Error(`The --alpha(\u2026) function only accepts one argument, e.g.: \`--alpha(${e||"var(--my-color)"} / ${o||"50%"})\``);return G(e,o)}function _r(t,r,...n){if(!r)throw new Error("The --spacing(\u2026) function requires an argument, but received none.");if(n.length>0)throw new Error(`The --spacing(\u2026) function only accepts a single argument, but received ${n.length+1}.`);let e=t.theme.resolve(null,["--spacing"]);if(!e)throw new Error("The --spacing(\u2026) function requires that the `--spacing` theme variable exists, but it was not found.");return`calc(${e} * ${r})`}function Fr(t,r,...n){if(!r.startsWith("--"))throw new Error("The --theme(\u2026) function can only be used with CSS variables from your theme.");return Et(t,r,...n)}function Et(t,r,...n){r=Ur(r);let e=t.resolveThemeValue(r);if(!e&&n.length>0)return n.join(", ");if(!e)throw new Error(`Could not resolve value for theme function: \`theme(${r})\`. Consider checking if the path is correct or provide a fallback value to silence this error.`);return e}var Tt=new RegExp(Object.keys(Be).map(t=>`${t}\\(`).join("|"));function ae(t,r){let n=0;return j(t,e=>{if(e.kind==="declaration"&&e.value&&Tt.test(e.value)){n|=8,e.value=St(e.value,r);return}e.kind==="at-rule"&&(e.name==="@media"||e.name==="@custom-media"||e.name==="@container"||e.name==="@supports")&&Tt.test(e.params)&&(n|=8,e.params=St(e.params,r))}),n}function St(t,r){let n=L(t);return le(n,(e,{replaceWith:o})=>{if(e.kind==="function"&&e.value in Be){let s=D(B(e.nodes).trim(),",").map(c=>c.trim()),a=Be[e.value](r,...s);return o(L(a))}}),B(n)}function Ur(t){if(t[0]!=="'"&&t[0]!=='"')return t;let r="",n=t[0];for(let e=1;e<t.length-1;e++){let o=t[e],s=t[e+1];o==="\\"&&(s===n||s==="\\")?(r+=s,e++):r+=o}return r}function Rt(t){let r=[];for(let n of t.utilities.keys("static"))r.push([n,{modifiers:[]}]);for(let n of t.utilities.keys("functional")){let e=t.utilities.getCompletions(n);for(let o of e)for(let s of o.values){let a=s===null?n:`${n}-${s}`;r.push([a,{modifiers:o.modifiers}]),o.supportsNegative&&r.push([`-${a}`,{modifiers:o.modifiers}])}}return r.sort((n,e)=>n[0]===e[0]?0:n[0]<e[0]?-1:1),r}function Kt(t){let r=[];for(let[e,o]of t.variants.entries()){let a=function({value:c,modifier:d}={}){let f=e;c&&(f+=`-${c}`),d&&(f+=`/${d}`);let p=t.parseVariant(f);if(!p)return[];let g=F(".__placeholder__",[]);if(se(g,p,t.variants)===null)return[];let h=[];return Te(g.nodes,(y,{path:w})=>{if(y.kind!=="rule"&&y.kind!=="at-rule"||y.nodes.length>0)return;w.sort((V,S)=>{let R=V.kind==="at-rule",E=S.kind==="at-rule";return R&&!E?-1:!R&&E?1:0});let v=w.flatMap(V=>V.kind==="rule"?V.selector==="&"?[]:[V.selector]:V.kind==="at-rule"?[`${V.name} ${V.params}`]:[]),x="";for(let V=v.length-1;V>=0;V--)x=x===""?v[V]:`${v[V]} { ${x} }`;h.push(x)}),h};var n=a;if(o.kind==="arbitrary")continue;let s=t.variants.getCompletions(e);switch(o.kind){case"static":{r.push({name:e,values:s,isArbitrary:!1,hasDash:!0,selectors:a});break}case"functional":{r.push({name:e,values:s,isArbitrary:!0,hasDash:!0,selectors:a});break}case"compound":{r.push({name:e,values:s,isArbitrary:!0,hasDash:!0,selectors:a});break}}}return r}function Pt(t,r){let{astNodes:n,nodeSorting:e}=Z(Array.from(r),t),o=new Map(r.map(a=>[a,null])),s=0n;for(let a of n){let c=e.get(a)?.candidate;c&&o.set(c,o.get(c)??s++)}return r.map(a=>[a,o.get(a)??null])}var Ee=/^@?[a-zA-Z0-9_-]*$/;var qe=class{compareFns=new Map;variants=new Map;completions=new Map;groupOrder=null;lastOrder=0;static(r,n,{compounds:e,order:o}={}){this.set(r,{kind:"static",applyFn:n,compoundsWith:0,compounds:e??2,order:o})}fromAst(r,n){let e=[];j(n,o=>{o.kind==="rule"?e.push(o.selector):o.kind==="at-rule"&&o.name!=="@slot"&&e.push(`${o.name} ${o.params}`)}),this.static(r,o=>{let s=structuredClone(n);Je(s,o.nodes),o.nodes=s},{compounds:ie(e)})}functional(r,n,{compounds:e,order:o}={}){this.set(r,{kind:"functional",applyFn:n,compoundsWith:0,compounds:e??2,order:o})}compound(r,n,e,{compounds:o,order:s}={}){this.set(r,{kind:"compound",applyFn:e,compoundsWith:n,compounds:o??2,order:s})}group(r,n){this.groupOrder=this.nextOrder(),n&&this.compareFns.set(this.groupOrder,n),r(),this.groupOrder=null}has(r){return this.variants.has(r)}get(r){return this.variants.get(r)}kind(r){return this.variants.get(r)?.kind}compoundsWith(r,n){let e=this.variants.get(r),o=typeof n=="string"?this.variants.get(n):n.kind==="arbitrary"?{compounds:ie([n.selector])}:this.variants.get(n.root);return!(!e||!o||e.kind!=="compound"||o.compounds===0||e.compoundsWith===0||!(e.compoundsWith&o.compounds))}suggest(r,n){this.completions.set(r,n)}getCompletions(r){return this.completions.get(r)?.()??[]}compare(r,n){if(r===n)return 0;if(r===null)return-1;if(n===null)return 1;if(r.kind==="arbitrary"&&n.kind==="arbitrary")return r.selector<n.selector?-1:1;if(r.kind==="arbitrary")return 1;if(n.kind==="arbitrary")return-1;let e=this.variants.get(r.root).order,o=this.variants.get(n.root).order,s=e-o;if(s!==0)return s;if(r.kind==="compound"&&n.kind==="compound"){let f=this.compare(r.variant,n.variant);return f!==0?f:r.modifier&&n.modifier?r.modifier.value<n.modifier.value?-1:1:r.modifier?1:n.modifier?-1:0}let a=this.compareFns.get(e);if(a!==void 0)return a(r,n);if(r.root!==n.root)return r.root<n.root?-1:1;let c=r.value,d=n.value;return c===null?-1:d===null||c.kind==="arbitrary"&&d.kind!=="arbitrary"?1:c.kind!=="arbitrary"&&d.kind==="arbitrary"||c.value<d.value?-1:1}keys(){return this.variants.keys()}entries(){return this.variants.entries()}set(r,{kind:n,applyFn:e,compounds:o,compoundsWith:s,order:a}){let c=this.variants.get(r);c?Object.assign(c,{kind:n,applyFn:e,compounds:o}):(a===void 0&&(this.lastOrder=this.nextOrder(),a=this.lastOrder),this.variants.set(r,{kind:n,applyFn:e,order:a,compoundsWith:s,compounds:o}))}nextOrder(){return this.groupOrder??this.lastOrder+1}};function ie(t){let r=0;for(let n of t){if(n[0]==="@"){if(!n.startsWith("@media")&&!n.startsWith("@supports")&&!n.startsWith("@container"))return 0;r|=1;continue}if(n.includes("::"))return 0;r|=2}return r}function jt(t){let r=new qe;function n(f,p,{compounds:g}={}){g=g??ie(p),r.static(f,h=>{h.nodes=p.map(y=>M(y,h.nodes))},{compounds:g})}r.static("force",()=>{},{compounds:0}),n("*",[":is(& > *)"],{compounds:0}),n("**",[":is(& *)"],{compounds:0});function e(f,p){return p.map(g=>{g=g.trim();let h=D(g," ");return h[0]==="not"?h.slice(1).join(" "):f==="@container"?h[0][0]==="("?`not ${g}`:h[1]==="not"?`${h[0]} ${h.slice(2).join(" ")}`:`${h[0]} not ${h.slice(1).join(" ")}`:`not ${g}`})}let o=["@media","@supports","@container"];function s(f){for(let p of o){if(p!==f.name)continue;let g=D(f.params,",");return g.length>1?null:(g=e(f.name,g),O(f.name,g.join(", ")))}return null}function a(f){return f.includes("::")?null:`&:not(${D(f,",").map(g=>(g.startsWith("&:is(")&&g.endsWith(")")&&(g=g.slice(5,-1)),g=g.replaceAll("&","*"),g)).join(", ")})`}r.compound("not",3,(f,p)=>{if(p.variant.kind==="arbitrary"&&p.variant.relative||p.modifier)return null;let g=!1;if(j([f],(h,{path:y})=>{if(h.kind!=="rule"&&h.kind!=="at-rule")return 0;if(h.nodes.length>0)return 0;let w=[],v=[];for(let V of y)V.kind==="at-rule"?w.push(V):V.kind==="rule"&&v.push(V);if(w.length>1)return 2;if(v.length>1)return 2;let x=[];for(let V of v){let S=a(V.selector);if(!S)return g=!1,2;x.push(F(S,[]))}for(let V of w){let S=s(V);if(!S)return g=!1,2;x.push(S)}return Object.assign(f,F("&",x)),g=!0,1}),f.kind==="rule"&&f.selector==="&"&&f.nodes.length===1&&Object.assign(f,f.nodes[0]),!g)return null}),r.suggest("not",()=>Array.from(r.keys()).filter(f=>r.compoundsWith("not",f))),r.compound("group",2,(f,p)=>{if(p.variant.kind==="arbitrary"&&p.variant.relative)return null;let g=p.modifier?`:where(.${t.prefix?`${t.prefix}\\:`:""}group\\/${p.modifier.value})`:`:where(.${t.prefix?`${t.prefix}\\:`:""}group)`,h=!1;if(j([f],(y,{path:w})=>{if(y.kind!=="rule")return 0;for(let x of w.slice(0,-1))if(x.kind==="rule")return h=!1,2;let v=y.selector.replaceAll("&",g);D(v,",").length>1&&(v=`:is(${v})`),y.selector=`&:is(${v} *)`,h=!0}),!h)return null}),r.suggest("group",()=>Array.from(r.keys()).filter(f=>r.compoundsWith("group",f))),r.compound("peer",2,(f,p)=>{if(p.variant.kind==="arbitrary"&&p.variant.relative)return null;let g=p.modifier?`:where(.${t.prefix?`${t.prefix}\\:`:""}peer\\/${p.modifier.value})`:`:where(.${t.prefix?`${t.prefix}\\:`:""}peer)`,h=!1;if(j([f],(y,{path:w})=>{if(y.kind!=="rule")return 0;for(let x of w.slice(0,-1))if(x.kind==="rule")return h=!1,2;let v=y.selector.replaceAll("&",g);D(v,",").length>1&&(v=`:is(${v})`),y.selector=`&:is(${v} ~ *)`,h=!0}),!h)return null}),r.suggest("peer",()=>Array.from(r.keys()).filter(f=>r.compoundsWith("peer",f))),n("first-letter",["&::first-letter"]),n("first-line",["&::first-line"]),n("marker",["& *::marker","&::marker"]),n("selection",["& *::selection","&::selection"]),n("file",["&::file-selector-button"]),n("placeholder",["&::placeholder"]),n("backdrop",["&::backdrop"]);{let f=function(){return _([O("@property","--tw-content",[l("syntax",'"*"'),l("initial-value",'""'),l("inherits","false")])])};var c=f;r.static("before",p=>{p.nodes=[F("&::before",[f(),l("content","var(--tw-content)"),...p.nodes])]},{compounds:0}),r.static("after",p=>{p.nodes=[F("&::after",[f(),l("content","var(--tw-content)"),...p.nodes])]},{compounds:0})}n("first",["&:first-child"]),n("last",["&:last-child"]),n("only",["&:only-child"]),n("odd",["&:nth-child(odd)"]),n("even",["&:nth-child(even)"]),n("first-of-type",["&:first-of-type"]),n("last-of-type",["&:last-of-type"]),n("only-of-type",["&:only-of-type"]),n("visited",["&:visited"]),n("target",["&:target"]),n("open",["&:is([open], :popover-open)"]),n("default",["&:default"]),n("checked",["&:checked"]),n("indeterminate",["&:indeterminate"]),n("placeholder-shown",["&:placeholder-shown"]),n("autofill",["&:autofill"]),n("optional",["&:optional"]),n("required",["&:required"]),n("valid",["&:valid"]),n("invalid",["&:invalid"]),n("in-range",["&:in-range"]),n("out-of-range",["&:out-of-range"]),n("read-only",["&:read-only"]),n("empty",["&:empty"]),n("focus-within",["&:focus-within"]),r.static("hover",f=>{f.nodes=[F("&:hover",[O("@media","(hover: hover)",f.nodes)])]}),n("focus",["&:focus"]),n("focus-visible",["&:focus-visible"]),n("active",["&:active"]),n("enabled",["&:enabled"]),n("disabled",["&:disabled"]),n("inert",["&:is([inert], [inert] *)"]),r.compound("in",2,(f,p)=>{if(p.modifier)return null;let g=!1;if(j([f],(h,{path:y})=>{if(h.kind!=="rule")return 0;for(let w of y.slice(0,-1))if(w.kind==="rule")return g=!1,2;h.selector=`:where(${h.selector.replaceAll("&","*")}) &`,g=!0}),!g)return null}),r.suggest("in",()=>Array.from(r.keys()).filter(f=>r.compoundsWith("in",f))),r.compound("has",2,(f,p)=>{if(p.modifier)return null;let g=!1;if(j([f],(h,{path:y})=>{if(h.kind!=="rule")return 0;for(let w of y.slice(0,-1))if(w.kind==="rule")return g=!1,2;h.selector=`&:has(${h.selector.replaceAll("&","*")})`,g=!0}),!g)return null}),r.suggest("has",()=>Array.from(r.keys()).filter(f=>r.compoundsWith("has",f))),r.functional("aria",(f,p)=>{if(!p.value||p.modifier)return null;p.value.kind==="arbitrary"?f.nodes=[F(`&[aria-${Ot(p.value.value)}]`,f.nodes)]:f.nodes=[F(`&[aria-${p.value.value}="true"]`,f.nodes)]}),r.suggest("aria",()=>["busy","checked","disabled","expanded","hidden","pressed","readonly","required","selected"]),r.functional("data",(f,p)=>{if(!p.value||p.modifier)return null;f.nodes=[F(`&[data-${Ot(p.value.value)}]`,f.nodes)]}),r.functional("nth",(f,p)=>{if(!p.value||p.modifier||p.value.kind==="named"&&!T(p.value.value))return null;f.nodes=[F(`&:nth-child(${p.value.value})`,f.nodes)]}),r.functional("nth-last",(f,p)=>{if(!p.value||p.modifier||p.value.kind==="named"&&!T(p.value.value))return null;f.nodes=[F(`&:nth-last-child(${p.value.value})`,f.nodes)]}),r.functional("nth-of-type",(f,p)=>{if(!p.value||p.modifier||p.value.kind==="named"&&!T(p.value.value))return null;f.nodes=[F(`&:nth-of-type(${p.value.value})`,f.nodes)]}),r.functional("nth-last-of-type",(f,p)=>{if(!p.value||p.modifier||p.value.kind==="named"&&!T(p.value.value))return null;f.nodes=[F(`&:nth-last-of-type(${p.value.value})`,f.nodes)]}),r.functional("supports",(f,p)=>{if(!p.value||p.modifier)return null;let g=p.value.value;if(g===null)return null;if(/^[\w-]*\s*\(/.test(g)){let h=g.replace(/\b(and|or|not)\b/g," $1 ");f.nodes=[O("@supports",h,f.nodes)];return}g.includes(":")||(g=`${g}: var(--tw)`),(g[0]!=="("||g[g.length-1]!==")")&&(g=`(${g})`),f.nodes=[O("@supports",g,f.nodes)]},{compounds:1}),n("motion-safe",["@media (prefers-reduced-motion: no-preference)"]),n("motion-reduce",["@media (prefers-reduced-motion: reduce)"]),n("contrast-more",["@media (prefers-contrast: more)"]),n("contrast-less",["@media (prefers-contrast: less)"]);{let f=function(p,g,h,y){if(p===g)return 0;let w=y.get(p);if(w===null)return h==="asc"?-1:1;let v=y.get(g);return v===null?h==="asc"?1:-1:re(w,v,h)};var d=f;{let p=t.namespace("--breakpoint"),g=new z(h=>{switch(h.kind){case"static":return t.resolveValue(h.root,["--breakpoint"])??null;case"functional":{if(!h.value||h.modifier)return null;let y=null;return h.value.kind==="arbitrary"?y=h.value.value:h.value.kind==="named"&&(y=t.resolveValue(h.value.value,["--breakpoint"])),!y||y.includes("var(")?null:y}case"arbitrary":case"compound":return null}});r.group(()=>{r.functional("max",(h,y)=>{if(y.modifier)return null;let w=g.get(y);if(w===null)return null;h.nodes=[O("@media",`(width < ${w})`,h.nodes)]},{compounds:1})},(h,y)=>f(h,y,"desc",g)),r.suggest("max",()=>Array.from(p.keys()).filter(h=>h!==null)),r.group(()=>{for(let[h,y]of t.namespace("--breakpoint"))h!==null&&r.static(h,w=>{w.nodes=[O("@media",`(width >= ${y})`,w.nodes)]},{compounds:1});r.functional("min",(h,y)=>{if(y.modifier)return null;let w=g.get(y);if(w===null)return null;h.nodes=[O("@media",`(width >= ${w})`,h.nodes)]},{compounds:1})},(h,y)=>f(h,y,"asc",g)),r.suggest("min",()=>Array.from(p.keys()).filter(h=>h!==null))}{let p=t.namespace("--container"),g=new z(h=>{switch(h.kind){case"functional":{if(h.value===null)return null;let y=null;return h.value.kind==="arbitrary"?y=h.value.value:h.value.kind==="named"&&(y=t.resolveValue(h.value.value,["--container"])),!y||y.includes("var(")?null:y}case"static":case"arbitrary":case"compound":return null}});r.group(()=>{r.functional("@max",(h,y)=>{let w=g.get(y);if(w===null)return null;h.nodes=[O("@container",y.modifier?`${y.modifier.value} (width < ${w})`:`(width < ${w})`,h.nodes)]},{compounds:1})},(h,y)=>f(h,y,"desc",g)),r.suggest("@max",()=>Array.from(p.keys()).filter(h=>h!==null)),r.group(()=>{r.functional("@",(h,y)=>{let w=g.get(y);if(w===null)return null;h.nodes=[O("@container",y.modifier?`${y.modifier.value} (width >= ${w})`:`(width >= ${w})`,h.nodes)]},{compounds:1}),r.functional("@min",(h,y)=>{let w=g.get(y);if(w===null)return null;h.nodes=[O("@container",y.modifier?`${y.modifier.value} (width >= ${w})`:`(width >= ${w})`,h.nodes)]},{compounds:1})},(h,y)=>f(h,y,"asc",g)),r.suggest("@min",()=>Array.from(p.keys()).filter(h=>h!==null))}}return n("portrait",["@media (orientation: portrait)"]),n("landscape",["@media (orientation: landscape)"]),n("ltr",['&:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *)']),n("rtl",['&:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *)']),n("dark",["@media (prefers-color-scheme: dark)"]),n("starting",["@starting-style"]),n("print",["@media print"]),n("forced-colors",["@media (forced-colors: active)"]),r}function Ot(t){if(t.includes("=")){let[r,...n]=D(t,"="),e=n.join("=").trim();if(e[0]==="'"||e[0]==='"')return t;if(e.length>1){let o=e[e.length-1];if(e[e.length-2]===" "&&(o==="i"||o==="I"||o==="s"||o==="S"))return`${r}="${e.slice(0,-2)}" ${o}`}return`${r}="${e}"`}return t}function Je(t,r){j(t,(n,{replaceWith:e})=>{if(n.kind==="at-rule"&&n.name==="@slot")e(r);else if(n.kind==="at-rule"&&(n.name==="@keyframes"||n.name==="@property"))return Object.assign(n,_([O(n.name,n.params,n.nodes)])),1})}function Dt(t){let r=Vt(t),n=jt(t),e=new z(c=>xt(c,a)),o=new z(c=>Array.from(kt(c,a))),s=new z(c=>_t(c,a)),a={theme:t,utilities:r,variants:n,invalidCandidates:new Set,important:!1,candidatesToCss(c){let d=[];for(let f of c){let p=!1,{astNodes:g}=Z([f],this,{onInvalidCandidate(){p=!0}});g=te(g),g.length===0||p?d.push(null):d.push(J(g))}return d},getClassOrder(c){return Pt(this,c)},getClassList(){return Rt(this)},getVariants(){return Kt(this)},parseCandidate(c){return o.get(c)},parseVariant(c){return e.get(c)},compileAstNodes(c){return s.get(c)},getVariantOrder(){let c=Array.from(e.values());c.sort((g,h)=>this.variants.compare(g,h));let d=new Map,f,p=0;for(let g of c)g!==null&&(f!==void 0&&this.variants.compare(f,g)!==0&&p++,d.set(g,p),f=g);return d},resolveThemeValue(c){let d=c.lastIndexOf("/"),f=null;d!==-1&&(f=c.slice(d+1).trim(),c=c.slice(0,d).trim());let p=t.get([c])??void 0;return f&&p?G(p,f):p}};return a}var Ge=["container-type","pointer-events","visibility","position","inset","inset-inline","inset-block","inset-inline-start","inset-inline-end","top","right","bottom","left","isolation","z-index","order","grid-column","grid-column-start","grid-column-end","grid-row","grid-row-start","grid-row-end","float","clear","--tw-container-component","margin","margin-inline","margin-block","margin-inline-start","margin-inline-end","margin-top","margin-right","margin-bottom","margin-left","box-sizing","display","field-sizing","aspect-ratio","height","max-height","min-height","width","max-width","min-width","flex","flex-shrink","flex-grow","flex-basis","table-layout","caption-side","border-collapse","border-spacing","transform-origin","translate","--tw-translate-x","--tw-translate-y","scale","--tw-scale-x","--tw-scale-y","--tw-scale-z","rotate","--tw-rotate-x","--tw-rotate-y","--tw-rotate-z","--tw-skew-x","--tw-skew-y","transform","animation","cursor","touch-action","--tw-pan-x","--tw-pan-y","--tw-pinch-zoom","resize","scroll-snap-type","--tw-scroll-snap-strictness","scroll-snap-align","scroll-snap-stop","scroll-margin","scroll-margin-inline","scroll-margin-block","scroll-margin-inline-start","scroll-margin-inline-end","scroll-margin-top","scroll-margin-right","scroll-margin-bottom","scroll-margin-left","scroll-padding","scroll-padding-inline","scroll-padding-block","scroll-padding-inline-start","scroll-padding-inline-end","scroll-padding-top","scroll-padding-right","scroll-padding-bottom","scroll-padding-left","list-style-position","list-style-type","list-style-image","appearance","columns","break-before","break-inside","break-after","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-template-columns","grid-template-rows","flex-direction","flex-wrap","place-content","place-items","align-content","align-items","justify-content","justify-items","gap","column-gap","row-gap","--tw-space-x-reverse","--tw-space-y-reverse","divide-x-width","divide-y-width","--tw-divide-y-reverse","divide-style","divide-color","place-self","align-self","justify-self","overflow","overflow-x","overflow-y","overscroll-behavior","overscroll-behavior-x","overscroll-behavior-y","scroll-behavior","border-radius","border-start-radius","border-end-radius","border-top-radius","border-right-radius","border-bottom-radius","border-left-radius","border-start-start-radius","border-start-end-radius","border-end-end-radius","border-end-start-radius","border-top-left-radius","border-top-right-radius","border-bottom-right-radius","border-bottom-left-radius","border-width","border-inline-width","border-block-width","border-inline-start-width","border-inline-end-width","border-top-width","border-right-width","border-bottom-width","border-left-width","border-style","border-inline-style","border-block-style","border-inline-start-style","border-inline-end-style","border-top-style","border-right-style","border-bottom-style","border-left-style","border-color","border-inline-color","border-block-color","border-inline-start-color","border-inline-end-color","border-top-color","border-right-color","border-bottom-color","border-left-color","background-color","background-image","--tw-gradient-position","--tw-gradient-stops","--tw-gradient-via-stops","--tw-gradient-from","--tw-gradient-from-position","--tw-gradient-via","--tw-gradient-via-position","--tw-gradient-to","--tw-gradient-to-position","box-decoration-break","background-size","background-attachment","background-clip","background-position","background-repeat","background-origin","fill","stroke","stroke-width","object-fit","object-position","padding","padding-inline","padding-block","padding-inline-start","padding-inline-end","padding-top","padding-right","padding-bottom","padding-left","text-align","text-indent","vertical-align","font-family","font-size","line-height","font-weight","letter-spacing","text-wrap","overflow-wrap","word-break","text-overflow","hyphens","white-space","color","text-transform","font-style","font-stretch","font-variant-numeric","text-decoration-line","text-decoration-color","text-decoration-style","text-decoration-thickness","text-underline-offset","-webkit-font-smoothing","placeholder-color","caret-color","accent-color","color-scheme","opacity","background-blend-mode","mix-blend-mode","box-shadow","--tw-shadow","--tw-shadow-color","--tw-ring-shadow","--tw-ring-color","--tw-inset-shadow","--tw-inset-shadow-color","--tw-inset-ring-shadow","--tw-inset-ring-color","--tw-ring-offset-width","--tw-ring-offset-color","outline","outline-width","outline-offset","outline-color","--tw-blur","--tw-brightness","--tw-contrast","--tw-drop-shadow","--tw-grayscale","--tw-hue-rotate","--tw-invert","--tw-saturate","--tw-sepia","filter","--tw-backdrop-blur","--tw-backdrop-brightness","--tw-backdrop-contrast","--tw-backdrop-grayscale","--tw-backdrop-hue-rotate","--tw-backdrop-invert","--tw-backdrop-opacity","--tw-backdrop-saturate","--tw-backdrop-sepia","backdrop-filter","transition-property","transition-behavior","transition-delay","transition-duration","transition-timing-function","will-change","contain","content","forced-color-adjust"];function Ft(t,r){let n=t.length,e=r.length,o=n<e?n:e;for(let s=0;s<o;s++){let a=t.charCodeAt(s),c=r.charCodeAt(s);if(a!==c){if(a>=48&&a<=57&&c>=48&&c<=57){let d=s,f=s+1,p=s,g=s+1;for(a=t.charCodeAt(f);a>=48&&a<=57;)a=t.charCodeAt(++f);for(c=r.charCodeAt(g);c>=48&&c<=57;)c=r.charCodeAt(++g);let h=t.slice(d,f),y=r.slice(p,g);return Number(h)-Number(y)||(h<y?-1:1)}return a-c}}return t.length-r.length}function Z(t,r,{onInvalidCandidate:n}={}){let e=new Map,o=[],s=new Map;for(let c of t){if(r.invalidCandidates.has(c)){n?.(c);continue}let d=r.parseCandidate(c);if(d.length===0){n?.(c);continue}s.set(c,d)}let a=r.getVariantOrder();for(let[c,d]of s){let f=!1;for(let p of d){let g=r.compileAstNodes(p);if(g.length!==0){try{ae(g.map(({node:h})=>h),r)}catch{continue}f=!0;for(let{node:h,propertySort:y}of g){let w=0n;for(let v of p.variants)w|=1n<<BigInt(a.get(v));e.set(h,{properties:y,variants:w,candidate:c}),o.push(h)}}}f||n?.(c)}return o.sort((c,d)=>{let f=e.get(c),p=e.get(d);if(f.variants-p.variants!==0n)return Number(f.variants-p.variants);let g=0;for(;f.properties.length<g&&p.properties.length<g&&f.properties[g]===p.properties[g];)g+=1;return(f.properties[g]??1/0)-(p.properties[g]??1/0)||p.properties.length-f.properties.length||Ft(f.candidate,p.candidate)}),{astNodes:o,nodeSorting:e}}function _t(t,r){let n=Ir(t,r);if(n.length===0)return[];let e=[],o=`.${xe(t.raw)}`;for(let s of n){let a=zr(s);(t.important||r.important)&&It(s);let c={kind:"rule",selector:o,nodes:s};for(let d of t.variants)if(se(c,d,r.variants)===null)return[];e.push({node:c,propertySort:a})}return e}function se(t,r,n,e=0){if(r.kind==="arbitrary"){if(r.relative&&e===0)return null;t.nodes=[M(r.selector,t.nodes)];return}let{applyFn:o}=n.get(r.root);if(r.kind==="compound"){let a=O("@slot");if(se(a,r.variant,n,e+1)===null||r.root==="not"&&a.nodes.length>1)return null;for(let d of a.nodes)if(d.kind!=="rule"&&d.kind!=="at-rule"||o(d,r)===null)return null;j(a.nodes,d=>{if((d.kind==="rule"||d.kind==="at-rule")&&d.nodes.length<=0)return d.nodes=t.nodes,1}),t.nodes=a.nodes;return}if(o(t,r)===null)return null}function Ut(t){let r=t.options?.types??[];return r.length>1&&r.includes("any")}function Ir(t,r){if(t.kind==="arbitrary"){let a=t.value;return t.modifier&&(a=W(a,t.modifier,r.theme)),a===null?[]:[[l(t.property,a)]]}let n=r.utilities.get(t.root)??[],e=[],o=n.filter(a=>!Ut(a));for(let a of o){if(a.kind!==t.kind)continue;let c=a.compileFn(t);if(c!==void 0){if(c===null)return e;e.push(c)}}if(e.length>0)return e;let s=n.filter(a=>Ut(a));for(let a of s){if(a.kind!==t.kind)continue;let c=a.compileFn(t);if(c!==void 0){if(c===null)return e;e.push(c)}}return e}function It(t){for(let r of t)r.kind!=="at-root"&&(r.kind==="declaration"?r.important=!0:(r.kind==="rule"||r.kind==="at-rule")&&It(r.nodes))}function zr(t){let r=new Set,n=t.slice();for(;n.length>0;){let e=n.shift();if(e.kind==="declaration"){if(e.property==="--tw-sort"){let s=Ge.indexOf(e.value??"");if(s!==-1){r.add(s);break}}let o=Ge.indexOf(e.property);o!==-1&&r.add(o)}else if(e.kind==="rule"||e.kind==="at-rule")for(let o of e.nodes)n.push(o)}return Array.from(r).sort((e,o)=>e-o)}function ve(t,r){let n=0,e=M("&",t),o=new Set,s=new z(()=>new Set),a=new z(()=>new Set);j([e],(g,{parent:h})=>{if(g.kind==="at-rule"){if(g.name==="@keyframes")return j(g.nodes,y=>{if(y.kind==="at-rule"&&y.name==="@apply")throw new Error("You cannot use `@apply` inside `@keyframes`.")}),1;if(g.name==="@utility"){let y=g.params.replace(/-\*$/,"");a.get(y).add(g),j(g.nodes,w=>{if(!(w.kind!=="at-rule"||w.name!=="@apply")){o.add(g);for(let v of zt(w,r))s.get(g).add(v)}});return}if(g.name==="@apply"){if(h===null)return;n|=1,o.add(h);for(let y of zt(g,r))s.get(h).add(y)}}});let c=new Set,d=[],f=new Set;function p(g,h=[]){if(!c.has(g)){if(f.has(g)){let y=h[(h.indexOf(g)+1)%h.length];throw g.kind==="at-rule"&&g.name==="@utility"&&y.kind==="at-rule"&&y.name==="@utility"&&j(g.nodes,w=>{if(w.kind!=="at-rule"||w.name!=="@apply")return;let v=w.params.split(/\s+/g);for(let x of v)for(let V of r.parseCandidate(x))switch(V.kind){case"arbitrary":break;case"static":case"functional":if(y.params.replace(/-\*$/,"")===V.root)throw new Error(`You cannot \`@apply\` the \`${x}\` utility here because it creates a circular dependency.`);break;default:}}),new Error(`Circular dependency detected:

${J([g])}
Relies on:

${J([y])}`)}f.add(g);for(let y of s.get(g))for(let w of a.get(y))h.push(g),p(w,h),h.pop();c.add(g),f.delete(g),d.push(g)}}for(let g of o)p(g);return j(d,(g,{replaceWith:h})=>{if(g.kind!=="at-rule"||g.name!=="@apply")return;let y=g.params.split(/\s+/g);{let w=Z(y,r,{onInvalidCandidate:x=>{throw new Error(`Cannot apply unknown utility class: ${x}`)}}).astNodes,v=[];for(let x of w)if(x.kind==="rule")for(let V of x.nodes)v.push(V);else v.push(x);h(v)}}),n}function*zt(t,r){for(let n of t.params.split(/\s+/g))for(let e of r.parseCandidate(n))switch(e.kind){case"arbitrary":break;case"static":case"functional":yield e.root;break;default:}}async function He(t,r,n,e=0){let o=0,s=[];return j(t,(a,{replaceWith:c})=>{if(a.kind==="at-rule"&&(a.name==="@import"||a.name==="@reference")){let d=Mr(L(a.params));if(d===null)return;a.name==="@reference"&&(d.media="reference"),o|=2;let{uri:f,layer:p,media:g,supports:h}=d;if(f.startsWith("data:")||f.startsWith("http://")||f.startsWith("https://"))return;let y=ee({},[]);return s.push((async()=>{if(e>100)throw new Error(`Exceeded maximum recursion depth while resolving \`${f}\` in \`${r}\`)`);let w=await n(f,r),v=X(w.content);await He(v,w.base,n,e+1),y.nodes=Lr([ee({base:w.base},v)],p,g,h)})()),c(y),1}}),s.length>0&&await Promise.all(s),o}function Mr(t){let r,n=null,e=null,o=null;for(let s=0;s<t.length;s++){let a=t[s];if(a.kind!=="separator"){if(a.kind==="word"&&!r){if(!a.value||a.value[0]!=='"'&&a.value[0]!=="'")return null;r=a.value.slice(1,-1);continue}if(a.kind==="function"&&a.value.toLowerCase()==="url"||!r)return null;if((a.kind==="word"||a.kind==="function")&&a.value.toLowerCase()==="layer"){if(n)return null;if(o)throw new Error("`layer(\u2026)` in an `@import` should come before any other functions or conditions");"nodes"in a?n=B(a.nodes):n="";continue}if(a.kind==="function"&&a.value.toLowerCase()==="supports"){if(o)return null;o=B(a.nodes);continue}e=B(t.slice(s));break}}return r?{uri:r,layer:n,media:e,supports:o}:null}function Lr(t,r,n,e){let o=t;return r!==null&&(o=[O("@layer",r,o)]),n!==null&&(o=[O("@media",n,o)]),e!==null&&(o=[O("@supports",e[0]==="("?e:`(${e})`,o)]),o}function ue(t,r=null){return Array.isArray(t)&&t.length===2&&typeof t[1]=="object"&&typeof t[1]!==null?r?t[1][r]??null:t[0]:Array.isArray(t)&&r===null?t.join(", "):typeof t=="string"&&r===null?t:null}function Lt(t,{theme:r},n){for(let e of n){let o=Re([e]);o&&t.theme.clearNamespace(`--${o}`,4)}for(let[e,o]of Wr(r)){if(typeof o!="string"&&typeof o!="number")continue;if(typeof o=="string"&&(o=o.replace(/<alpha-value>/g,"1")),e[0]==="opacity"&&(typeof o=="number"||typeof o=="string")){let a=typeof o=="string"?parseFloat(o):o;a>=0&&a<=1&&(o=a*100+"%")}let s=Re(e);s&&t.theme.add(`--${xe(s)}`,""+o,7)}if(Object.hasOwn(r,"fontFamily")){let e=5;{let o=ue(r.fontFamily.sans);o&&t.theme.hasDefault("--font-sans")&&(t.theme.add("--default-font-family",o,e),t.theme.add("--default-font-feature-settings",ue(r.fontFamily.sans,"fontFeatureSettings")??"normal",e),t.theme.add("--default-font-variation-settings",ue(r.fontFamily.sans,"fontVariationSettings")??"normal",e))}{let o=ue(r.fontFamily.mono);o&&t.theme.hasDefault("--font-mono")&&(t.theme.add("--default-mono-font-family",o,e),t.theme.add("--default-mono-font-feature-settings",ue(r.fontFamily.mono,"fontFeatureSettings")??"normal",e),t.theme.add("--default-mono-font-variation-settings",ue(r.fontFamily.mono,"fontVariationSettings")??"normal",e))}}return r}function Wr(t){let r=[];return Wt(t,[],(n,e)=>{if(qr(n))return r.push([e,n]),1;if(Jr(n)){r.push([e,n[0]]);for(let o of Reflect.ownKeys(n[1]))r.push([[...e,`-${o}`],n[1][o]]);return 1}if(Array.isArray(n)&&n.every(o=>typeof o=="string"))return r.push([e,n.join(", ")]),1}),r}var Br=/^[a-zA-Z0-9-_%/\.]+$/;function Re(t){if(t[0]==="container")return null;t=structuredClone(t),t[0]==="animation"&&(t[0]="animate"),t[0]==="aspectRatio"&&(t[0]="aspect"),t[0]==="borderRadius"&&(t[0]="radius"),t[0]==="boxShadow"&&(t[0]="shadow"),t[0]==="colors"&&(t[0]="color"),t[0]==="fontFamily"&&(t[0]="font"),t[0]==="fontSize"&&(t[0]="text"),t[0]==="letterSpacing"&&(t[0]="tracking"),t[0]==="lineHeight"&&(t[0]="leading"),t[0]==="maxWidth"&&(t[0]="container"),t[0]==="screens"&&(t[0]="breakpoint"),t[0]==="transitionTimingFunction"&&(t[0]="ease");for(let r of t)if(!Br.test(r))return null;return t.map((r,n,e)=>r==="1"&&n!==e.length-1?"":r).map(r=>r.replaceAll(".","_").replace(/([a-z])([A-Z])/g,(n,e,o)=>`${e}-${o.toLowerCase()}`)).filter((r,n)=>r!=="DEFAULT"||n!==t.length-1).join("-")}function qr(t){return typeof t=="number"||typeof t=="string"}function Jr(t){if(!Array.isArray(t)||t.length!==2||typeof t[0]!="string"&&typeof t[0]!="number"||t[1]===void 0||t[1]===null||typeof t[1]!="object")return!1;for(let r of Reflect.ownKeys(t[1]))if(typeof r!="string"||typeof t[1][r]!="string"&&typeof t[1][r]!="number")return!1;return!0}function Wt(t,r=[],n){for(let e of Reflect.ownKeys(t)){let o=t[e];if(o==null)continue;let s=[...r,e],a=n(o,s)??0;if(a!==1){if(a===2)return 2;if(!(!Array.isArray(o)&&typeof o!="object")&&Wt(o,s,n)===2)return 2}}}function Ke(t){let r=[];for(let n of D(t,".")){if(!n.includes("[")){r.push(n);continue}let e=0;for(;;){let o=n.indexOf("[",e),s=n.indexOf("]",o);if(o===-1||s===-1)break;o>e&&r.push(n.slice(e,o)),r.push(n.slice(o+1,s)),e=s+1}e<=n.length-1&&r.push(n.slice(e))}return r}function fe(t){if(Object.prototype.toString.call(t)!=="[object Object]")return!1;let r=Object.getPrototypeOf(t);return r===null||Object.getPrototypeOf(r)===null}function ye(t,r,n,e=[]){for(let o of r)if(o!=null)for(let s of Reflect.ownKeys(o)){e.push(s);let a=n(t[s],o[s],e);a!==void 0?t[s]=a:!fe(t[s])||!fe(o[s])?t[s]=o[s]:t[s]=ye({},[t[s],o[s]],n,e),e.pop()}return t}function Pe(t,r,n){return function(o,s){let a=o.lastIndexOf("/"),c=null;a!==-1&&(c=o.slice(a+1).trim(),o=o.slice(0,a).trim());let d=(()=>{let f=Ke(o),[p,g]=Gr(t.theme,f),h=n(Bt(r()??{},f)??null);if(typeof h=="string"&&(h=h.replace("<alpha-value>","1")),typeof p!="object")return typeof g!="object"&&g&4?h??p:p;if(h!==null&&typeof h=="object"&&!Array.isArray(h)){let y=ye({},[h],(w,v)=>v);if(p===null&&Object.hasOwn(h,"__CSS_VALUES__")){let w={};for(let v in h.__CSS_VALUES__)w[v]=h[v],delete y[v];p=w}for(let w in p)w!=="__CSS_VALUES__"&&(h?.__CSS_VALUES__?.[w]&4&&Bt(y,w.split("-"))!==void 0||(y[tt(w)]=p[w]));return y}if(Array.isArray(p)&&Array.isArray(g)&&Array.isArray(h)){let y=p[0],w=p[1];g[0]&4&&(y=h[0]??y);for(let v of Object.keys(w))g[1][v]&4&&(w[v]=h[1][v]??w[v]);return[y,w]}return p??h})();return c&&typeof d=="string"&&(d=G(d,c)),d??s}}function Gr(t,r){if(r.length===1&&r[0].startsWith("--"))return[t.get([r[0]]),t.getOptions(r[0])];let n=Re(r),e=new Map,o=new z(()=>new Map),s=t.namespace(`--${n}`);if(s.size===0)return[null,0];let a=new Map;for(let[p,g]of s){if(!p||!p.includes("--")){e.set(p,g),a.set(p,t.getOptions(p?`--${n}-${p}`:`--${n}`));continue}let h=p.indexOf("--"),y=p.slice(0,h),w=p.slice(h+2);w=w.replace(/-([a-z])/g,(v,x)=>x.toUpperCase()),o.get(y===""?null:y).set(w,[g,t.getOptions(`--${n}${p}`)])}let c=t.getOptions(`--${n}`);for(let[p,g]of o){let h=e.get(p);if(typeof h!="string")continue;let y={},w={};for(let[v,[x,V]]of g)y[v]=x,w[v]=V;e.set(p,[h,y]),a.set(p,[c,w])}let d={},f={};for(let[p,g]of e)qt(d,[p??"DEFAULT"],g);for(let[p,g]of a)qt(f,[p??"DEFAULT"],g);return r[r.length-1]==="DEFAULT"?[d?.DEFAULT??null,f.DEFAULT??0]:"DEFAULT"in d&&Object.keys(d).length===1?[d.DEFAULT,f.DEFAULT??0]:(d.__CSS_VALUES__=f,[d,f])}function Bt(t,r){for(let n=0;n<r.length;++n){let e=r[n];if(t[e]===void 0){if(r[n+1]===void 0)return;r[n+1]=`${e}-${r[n+1]}`;continue}t=t[e]}return t}function qt(t,r,n){for(let e of r.slice(0,-1))t[e]===void 0&&(t[e]={}),t=t[e];t[r[r.length-1]]=n}function Hr(t){return{kind:"combinator",value:t}}function Yr(t,r){return{kind:"function",value:t,nodes:r}}function we(t){return{kind:"selector",value:t}}function Zr(t){return{kind:"separator",value:t}}function Qr(t){return{kind:"value",value:t}}function Oe(t,r,n=null){for(let e=0;e<t.length;e++){let o=t[e],s=r(o,{parent:n,replaceWith(a){Array.isArray(a)?a.length===0?t.splice(e,1):a.length===1?t[e]=a[0]:t.splice(e,1,...a):t[e]=a,e--}})??0;if(s===2)return 2;if(s!==1&&o.kind==="function"&&Oe(o.nodes,r,o)===2)return 2}}function je(t){let r="";for(let n of t)switch(n.kind){case"combinator":case"selector":case"separator":case"value":{r+=n.value;break}case"function":r+=n.value+"("+je(n.nodes)+")"}return r}var Jt=92,Xr=93,Gt=41,en=58,Ht=44,tn=34,rn=46,Yt=62,Zt=10,nn=35,Qt=91,Xt=40,er=43,on=39,tr=32,rr=9,nr=126;function Ye(t){t=t.replaceAll(`\r
`,`
`);let r=[],n=[],e=null,o="",s;for(let a=0;a<t.length;a++){let c=t.charCodeAt(a);switch(c){case Ht:case Yt:case Zt:case tr:case er:case rr:case nr:{if(o.length>0){let h=we(o);e?e.nodes.push(h):r.push(h),o=""}let d=a,f=a+1;for(;f<t.length&&(s=t.charCodeAt(f),!(s!==Ht&&s!==Yt&&s!==Zt&&s!==tr&&s!==er&&s!==rr&&s!==nr));f++);a=f-1;let p=t.slice(d,f),g=p.trim()===","?Zr(p):Hr(p);e?e.nodes.push(g):r.push(g);break}case Xt:{let d=Yr(o,[]);if(o="",d.value!==":not"&&d.value!==":where"&&d.value!==":has"&&d.value!==":is"){let f=a+1,p=0;for(let h=a+1;h<t.length;h++){if(s=t.charCodeAt(h),s===Xt){p++;continue}if(s===Gt){if(p===0){a=h;break}p--}}let g=a;d.nodes.push(Qr(t.slice(f,g))),o="",a=g,r.push(d);break}e?e.nodes.push(d):r.push(d),n.push(d),e=d;break}case Gt:{let d=n.pop();if(o.length>0){let f=we(o);d.nodes.push(f),o=""}n.length>0?e=n[n.length-1]:e=null;break}case rn:case en:case nn:{if(o.length>0){let d=we(o);e?e.nodes.push(d):r.push(d)}o=String.fromCharCode(c);break}case Qt:{if(o.length>0){let p=we(o);e?e.nodes.push(p):r.push(p)}o="";let d=a,f=0;for(let p=a+1;p<t.length;p++){if(s=t.charCodeAt(p),s===Qt){f++;continue}if(s===Xr){if(f===0){a=p;break}f--}}o+=t.slice(d,a+1);break}case on:case tn:{let d=a;for(let f=a+1;f<t.length;f++)if(s=t.charCodeAt(f),s===Jt)f+=1;else if(s===c){a=f;break}o+=t.slice(d,a+1);break}case Jt:{let d=t.charCodeAt(a+1);o+=String.fromCharCode(c)+String.fromCharCode(d),a+=1;break}default:o+=String.fromCharCode(c)}}return o.length>0&&r.push(we(o)),r}var ir=/^[a-z@][a-zA-Z0-9/%._-]*$/;function Ze({designSystem:t,ast:r,resolvedConfig:n,featuresRef:e,referenceMode:o}){let s={addBase(a){if(o)return;let c=H(a);e.current|=ae(c,t),r.push(O("@layer","base",c))},addVariant(a,c){if(!Ee.test(a))throw new Error(`\`addVariant('${a}')\` defines an invalid variant name. Variants should only contain alphanumeric, dashes or underscore characters.`);typeof c=="string"||Array.isArray(c)?t.variants.static(a,d=>{d.nodes=or(c,d.nodes)},{compounds:ie(typeof c=="string"?[c]:c)}):typeof c=="object"&&t.variants.fromAst(a,H(c))},matchVariant(a,c,d){function f(g,h,y){let w=c(g,{modifier:h?.value??null});return or(w,y)}let p=Object.keys(d?.values??{});t.variants.group(()=>{t.variants.functional(a,(g,h)=>{if(!h.value){if(d?.values&&"DEFAULT"in d.values){g.nodes=f(d.values.DEFAULT,h.modifier,g.nodes);return}return null}if(h.value.kind==="arbitrary")g.nodes=f(h.value.value,h.modifier,g.nodes);else if(h.value.kind==="named"&&d?.values){let y=d.values[h.value.value];if(typeof y!="string")return;g.nodes=f(y,h.modifier,g.nodes)}})},(g,h)=>{if(g.kind!=="functional"||h.kind!=="functional")return 0;let y=g.value?g.value.value:"DEFAULT",w=h.value?h.value.value:"DEFAULT",v=d?.values?.[y]??y,x=d?.values?.[w]??w;if(d&&typeof d.sort=="function")return d.sort({value:v,modifier:g.modifier?.value??null},{value:x,modifier:h.modifier?.value??null});let V=p.indexOf(y),S=p.indexOf(w);return V=V===-1?p.length:V,S=S===-1?p.length:S,V!==S?V-S:v<x?-1:1})},addUtilities(a){a=Array.isArray(a)?a:[a];let c=a.flatMap(f=>Object.entries(f));c=c.flatMap(([f,p])=>D(f,",").map(g=>[g.trim(),p]));let d=new z(()=>[]);for(let[f,p]of c){if(f.startsWith("@keyframes ")){o||r.push(M(f,H(p)));continue}let g=Ye(f),h=!1;if(Oe(g,y=>{if(y.kind==="selector"&&y.value[0]==="."&&ir.test(y.value.slice(1))){let w=y.value;y.value="&";let v=je(g),x=w.slice(1),V=v==="&"?H(p):[M(v,H(p))];d.get(x).push(...V),h=!0,y.value=w;return}if(y.kind==="function"&&y.value===":not")return 1}),!h)throw new Error(`\`addUtilities({ '${f}' : \u2026 })\` defines an invalid utility selector. Utilities must be a single class name and start with a lowercase letter, eg. \`.scrollbar-none\`.`)}for(let[f,p]of d)t.theme.prefix&&j(p,g=>{if(g.kind==="rule"){let h=Ye(g.selector);Oe(h,y=>{y.kind==="selector"&&y.value[0]==="."&&(y.value=`.${t.theme.prefix}\\:${y.value.slice(1)}`)}),g.selector=je(h)}}),t.utilities.static(f,()=>{let g=structuredClone(p);return e.current|=ve(g,t),g})},matchUtilities(a,c){let d=c?.type?Array.isArray(c?.type)?c.type:[c.type]:["any"];for(let[p,g]of Object.entries(a)){let h=function({negative:y}){return w=>{if(w.value?.kind==="arbitrary"&&d.length>0&&!d.includes("any")&&(w.value.dataType&&!d.includes(w.value.dataType)||!w.value.dataType&&!I(w.value.value,d)))return;let v=d.includes("color"),x=null,V=!1;{let E=c?.values??{};v&&(E=Object.assign({inherit:"inherit",transparent:"transparent",current:"currentColor"},E)),w.value?w.value.kind==="arbitrary"?x=w.value.value:w.value.fraction&&E[w.value.fraction]?(x=E[w.value.fraction],V=!0):E[w.value.value]?x=E[w.value.value]:E.__BARE_VALUE__&&(x=E.__BARE_VALUE__(w.value)??null,V=(w.value.fraction!==null&&x?.includes("/"))??!1):x=E.DEFAULT??null}if(x===null)return;let S;{let E=c?.modifiers??null;w.modifier?E==="any"||w.modifier.kind==="arbitrary"?S=w.modifier.value:E?.[w.modifier.value]?S=E[w.modifier.value]:v&&!Number.isNaN(Number(w.modifier.value))?S=`${w.modifier.value}%`:S=null:S=null}if(w.modifier&&S===null&&!V)return w.value?.kind==="arbitrary"?null:void 0;v&&S!==null&&(x=G(x,S)),y&&(x=`calc(${x} * -1)`);let R=H(g(x,{modifier:S}));return e.current|=ve(R,t),R}};var f=h;if(!ir.test(p))throw new Error(`\`matchUtilities({ '${p}' : \u2026 })\` defines an invalid utility name. Utilities should be alphanumeric and start with a lowercase letter, eg. \`scrollbar\`.`);c?.supportsNegativeValues&&t.utilities.functional(`-${p}`,h({negative:!0}),{types:d}),t.utilities.functional(p,h({negative:!1}),{types:d}),t.utilities.suggest(p,()=>{let y=c?.values??{},w=new Set(Object.keys(y));w.delete("__BARE_VALUE__"),w.has("DEFAULT")&&(w.delete("DEFAULT"),w.add(null));let v=c?.modifiers??{},x=v==="any"?[]:Object.keys(v);return[{supportsNegative:c?.supportsNegativeValues??!1,values:Array.from(w),modifiers:x}]})}},addComponents(a,c){this.addUtilities(a,c)},matchComponents(a,c){this.matchUtilities(a,c)},theme:Pe(t,()=>n.theme??{},a=>a),prefix(a){return a},config(a,c){let d=n;if(!a)return d;let f=Ke(a);for(let p=0;p<f.length;++p){let g=f[p];if(d[g]===void 0)return c;d=d[g]}return d??c}};return s.addComponents=s.addComponents.bind(s),s.matchComponents=s.matchComponents.bind(s),s}function H(t){let r=[];t=Array.isArray(t)?t:[t];let n=t.flatMap(e=>Object.entries(e));for(let[e,o]of n)if(typeof o!="object")!e.startsWith("--")&&o==="@slot"?r.push(M(e,[O("@slot")])):(e=e.replace(/([A-Z])/g,"-$1").toLowerCase(),r.push(l(e,String(o))));else if(Array.isArray(o))for(let s of o)typeof s=="string"?r.push(l(e,s)):r.push(M(e,H(s)));else o!==null&&r.push(M(e,H(o)));return r}function or(t,r){return(typeof t=="string"?[t]:t).flatMap(e=>{if(e.trim().endsWith("}")){let o=e.replace("}","{@slot}}"),s=X(o);return Je(s,r),s}else return M(e,r)})}function lr(t,r,n){for(let e of an(r))t.theme.addKeyframes(e)}function an(t){let r=[];if("keyframes"in t.theme)for(let[n,e]of Object.entries(t.theme.keyframes))r.push(O("@keyframes",n,H(e)));return r}function ar(t){return{theme:{...it,colors:({theme:r})=>r("color",{}),extend:{fontSize:({theme:r})=>({...r("text",{})}),boxShadow:({theme:r})=>({...r("shadow",{})}),animation:({theme:r})=>({...r("animate",{})}),aspectRatio:({theme:r})=>({...r("aspect",{})}),borderRadius:({theme:r})=>({...r("radius",{})}),screens:({theme:r})=>({...r("breakpoint",{})}),letterSpacing:({theme:r})=>({...r("tracking",{})}),lineHeight:({theme:r})=>({...r("leading",{})}),transitionDuration:{DEFAULT:t.get(["--default-transition-duration"])??null},transitionTimingFunction:{DEFAULT:t.get(["--default-transition-timing-function"])??null},maxWidth:({theme:r})=>({...r("container",{})})}}}}var sn={blocklist:[],future:{},prefix:"",important:!1,darkMode:null,theme:{},plugins:[],content:{files:[]}};function Xe(t,r){let n={design:t,configs:[],plugins:[],content:{files:[]},theme:{},extend:{},result:structuredClone(sn)};for(let o of r)Qe(n,o);for(let o of n.configs)"darkMode"in o&&o.darkMode!==void 0&&(n.result.darkMode=o.darkMode??null),"prefix"in o&&o.prefix!==void 0&&(n.result.prefix=o.prefix??""),"blocklist"in o&&o.blocklist!==void 0&&(n.result.blocklist=o.blocklist??[]),"important"in o&&o.important!==void 0&&(n.result.important=o.important??!1);let e=fn(n);return{resolvedConfig:{...n.result,content:n.content,theme:n.theme,plugins:n.plugins},replacedThemeKeys:e}}function un(t,r){if(Array.isArray(t)&&fe(t[0]))return t.concat(r);if(Array.isArray(r)&&fe(r[0])&&fe(t))return[t,...r];if(Array.isArray(r))return r}function Qe(t,{config:r,base:n,path:e,reference:o}){let s=[];for(let d of r.plugins??[])"__isOptionsFunction"in d?s.push({...d(),reference:o}):"handler"in d?s.push({...d,reference:o}):s.push({handler:d,reference:o});if(Array.isArray(r.presets)&&r.presets.length===0)throw new Error("Error in the config file/plugin/preset. An empty preset (`preset: []`) is not currently supported.");for(let d of r.presets??[])Qe(t,{path:e,base:n,config:d,reference:o});for(let d of s)t.plugins.push(d),d.config&&Qe(t,{path:e,base:n,config:d.config,reference:!!d.reference});let a=r.content??[],c=Array.isArray(a)?a:a.files;for(let d of c)t.content.files.push(typeof d=="object"?d:{base:n,pattern:d});t.configs.push(r)}function fn(t){let r=new Set,n=Pe(t.design,()=>t.theme,o),e=Object.assign(n,{theme:n,colors:nt});function o(s){return typeof s=="function"?s(e)??null:s??null}for(let s of t.configs){let a=s.theme??{},c=a.extend??{};for(let d in a)d!=="extend"&&r.add(d);Object.assign(t.theme,a);for(let d in c)t.extend[d]??=[],t.extend[d].push(c[d])}delete t.theme.extend;for(let s in t.extend){let a=[t.theme[s],...t.extend[s]];t.theme[s]=()=>{let c=a.map(o);return ye({},c,un)}}for(let s in t.theme)t.theme[s]=o(t.theme[s]);if(t.theme.screens&&typeof t.theme.screens=="object")for(let s of Object.keys(t.theme.screens)){let a=t.theme.screens[s];a&&typeof a=="object"&&("raw"in a||"max"in a||"min"in a&&(t.theme.screens[s]=a.min))}return r}function sr(t,r){let n=t.theme.container||{};if(typeof n!="object"||n===null)return;let e=cn(n,r);e.length!==0&&r.utilities.static("container",()=>structuredClone(e))}function cn({center:t,padding:r,screens:n},e){let o=[],s=null;if(t&&o.push(l("margin-inline","auto")),(typeof r=="string"||typeof r=="object"&&r!==null&&"DEFAULT"in r)&&o.push(l("padding-inline",typeof r=="string"?r:r.DEFAULT)),typeof n=="object"&&n!==null){s=new Map;let a=Array.from(e.theme.namespace("--breakpoint").entries());if(a.sort((c,d)=>re(c[1],d[1],"asc")),a.length>0){let[c]=a[0];o.push(O("@media",`(width >= --theme(--breakpoint-${c}))`,[l("max-width","none")]))}for(let[c,d]of Object.entries(n)){if(typeof d=="object")if("min"in d)d=d.min;else continue;s.set(c,O("@media",`(width >= ${d})`,[l("max-width",d)]))}}if(typeof r=="object"&&r!==null){let a=Object.entries(r).filter(([c])=>c!=="DEFAULT").map(([c,d])=>[c,e.theme.resolveValue(c,["--breakpoint"]),d]).filter(Boolean);a.sort((c,d)=>re(c[1],d[1],"asc"));for(let[c,,d]of a)if(s&&s.has(c))s.get(c).nodes.push(l("padding-inline",d));else{if(s)continue;o.push(O("@media",`(width >= theme(--breakpoint-${c}))`,[l("padding-inline",d)]))}}if(s)for(let[,a]of s)o.push(a);return o}function ur({addVariant:t,config:r}){let n=r("darkMode",null),[e,o=".dark"]=Array.isArray(n)?n:[n];if(e==="variant"){let s;if(Array.isArray(o)||typeof o=="function"?s=o:typeof o=="string"&&(s=[o]),Array.isArray(s))for(let a of s)a===".dark"?(e=!1,console.warn('When using `variant` for `darkMode`, you must provide a selector.\nExample: `darkMode: ["variant", ".your-selector &"]`')):a.includes("&")||(e=!1,console.warn('When using `variant` for `darkMode`, your selector must contain `&`.\nExample `darkMode: ["variant", ".your-selector &"]`'));o=s}e===null||(e==="selector"?t("dark",`&:where(${o}, ${o} *)`):e==="media"?t("dark","@media (prefers-color-scheme: dark)"):e==="variant"?t("dark",o):e==="class"&&t("dark",`&:is(${o} *)`))}function fr(t){for(let[r,n]of[["t","top"],["tr","top right"],["r","right"],["br","bottom right"],["b","bottom"],["bl","bottom left"],["l","left"],["tl","top left"]])t.utilities.static(`bg-gradient-to-${r}`,()=>[l("--tw-gradient-position",`to ${n} in oklab,`),l("background-image","linear-gradient(var(--tw-gradient-stops))")]);t.utilities.functional("max-w-screen",r=>{if(!r.value||r.value.kind==="arbitrary")return;let n=t.theme.resolve(r.value.value,["--breakpoint"]);if(n)return[l("max-width",n)]}),t.utilities.static("overflow-ellipsis",()=>[l("text-overflow","ellipsis")]),t.utilities.static("decoration-slice",()=>[l("-webkit-box-decoration-break","slice"),l("box-decoration-break","slice")]),t.utilities.static("decoration-clone",()=>[l("-webkit-box-decoration-break","clone"),l("box-decoration-break","clone")]),t.utilities.functional("flex-shrink",r=>{if(!r.modifier){if(!r.value)return[l("flex-shrink","1")];if(r.value.kind==="arbitrary")return[l("flex-shrink",r.value.value)];if(T(r.value.value))return[l("flex-shrink",r.value.value)]}}),t.utilities.functional("flex-grow",r=>{if(!r.modifier){if(!r.value)return[l("flex-grow","1")];if(r.value.kind==="arbitrary")return[l("flex-grow",r.value.value)];if(T(r.value.value))return[l("flex-grow",r.value.value)]}})}function cr(t,r){let n=t.theme.screens||{},e=r.variants.get("min")?.order??0,o=[];for(let[a,c]of Object.entries(n)){let h=function(y){r.variants.static(a,w=>{w.nodes=[O("@media",g,w.nodes)]},{order:y})};var s=h;let d=r.variants.get(a),f=r.theme.resolveValue(a,["--breakpoint"]);if(d&&f&&!r.theme.hasDefault(`--breakpoint-${a}`))continue;let p=!0;typeof c=="string"&&(p=!1);let g=dn(c);p?o.push(h):h(e)}if(o.length!==0){for(let[,a]of r.variants.variants)a.order>e&&(a.order+=o.length);r.variants.compareFns=new Map(Array.from(r.variants.compareFns).map(([a,c])=>(a>e&&(a+=o.length),[a,c])));for(let[a,c]of o.entries())c(e+a+1)}}function dn(t){return(Array.isArray(t)?t:[t]).map(n=>typeof n=="string"?{min:n}:n&&typeof n=="object"?n:null).map(n=>{if(n===null)return null;if("raw"in n)return n.raw;let e="";return n.max!==void 0&&(e+=`${n.max} >= `),e+="width",n.min!==void 0&&(e+=` >= ${n.min}`),`(${e})`}).filter(Boolean).join(", ")}function dr(t,r){let n=t.theme.aria||{},e=t.theme.supports||{},o=t.theme.data||{};if(Object.keys(n).length>0){let s=r.variants.get("aria"),a=s?.applyFn,c=s?.compounds;r.variants.functional("aria",(d,f)=>{let p=f.value;return p&&p.kind==="named"&&p.value in n?a?.(d,{...f,value:{kind:"arbitrary",value:n[p.value]}}):a?.(d,f)},{compounds:c})}if(Object.keys(e).length>0){let s=r.variants.get("supports"),a=s?.applyFn,c=s?.compounds;r.variants.functional("supports",(d,f)=>{let p=f.value;return p&&p.kind==="named"&&p.value in e?a?.(d,{...f,value:{kind:"arbitrary",value:e[p.value]}}):a?.(d,f)},{compounds:c})}if(Object.keys(o).length>0){let s=r.variants.get("data"),a=s?.applyFn,c=s?.compounds;r.variants.functional("data",(d,f)=>{let p=f.value;return p&&p.kind==="named"&&p.value in o?a?.(d,{...f,value:{kind:"arbitrary",value:o[p.value]}}):a?.(d,f)},{compounds:c})}}var pn=/^[a-z]+$/;async function gr({designSystem:t,base:r,ast:n,loadModule:e,globs:o}){let s=0,a=[],c=[];j(n,(g,{parent:h,replaceWith:y,context:w})=>{if(g.kind==="at-rule"){if(g.name==="@plugin"){if(h!==null)throw new Error("`@plugin` cannot be nested.");let v=g.params.slice(1,-1);if(v.length===0)throw new Error("`@plugin` must have a path.");let x={};for(let V of g.nodes??[]){if(V.kind!=="declaration")throw new Error(`Unexpected \`@plugin\` option:

${J([V])}

\`@plugin\` options must be a flat list of declarations.`);if(V.value===void 0)continue;let S=V.value,R=D(S,",").map(E=>{if(E=E.trim(),E==="null")return null;if(E==="true")return!0;if(E==="false")return!1;if(Number.isNaN(Number(E))){if(E[0]==='"'&&E[E.length-1]==='"'||E[0]==="'"&&E[E.length-1]==="'")return E.slice(1,-1);if(E[0]==="{"&&E[E.length-1]==="}")throw new Error(`Unexpected \`@plugin\` option: Value of declaration \`${J([V]).trim()}\` is not supported.

Using an object as a plugin option is currently only supported in JavaScript configuration files.`)}else return Number(E);return E});x[V.property]=R.length===1?R[0]:R}a.push([{id:v,base:w.base,reference:!!w.reference},Object.keys(x).length>0?x:null]),y([]),s|=4;return}if(g.name==="@config"){if(g.nodes.length>0)throw new Error("`@config` cannot have a body.");if(h!==null)throw new Error("`@config` cannot be nested.");c.push({id:g.params.slice(1,-1),base:w.base,reference:!!w.reference}),y([]),s|=4;return}}}),fr(t);let d=t.resolveThemeValue;if(t.resolveThemeValue=function(h){return h.startsWith("--")?d(h):(s|=pr({designSystem:t,base:r,ast:n,globs:o,configs:[],pluginDetails:[]}),t.resolveThemeValue(h))},!a.length&&!c.length)return 0;let[f,p]=await Promise.all([Promise.all(c.map(async({id:g,base:h,reference:y})=>{let w=await e(g,h,"config");return{path:g,base:w.base,config:w.module,reference:y}})),Promise.all(a.map(async([{id:g,base:h,reference:y},w])=>{let v=await e(g,h,"plugin");return{path:g,base:v.base,plugin:v.module,options:w,reference:y}}))]);return s|=pr({designSystem:t,base:r,ast:n,globs:o,configs:f,pluginDetails:p}),s}function pr({designSystem:t,base:r,ast:n,globs:e,configs:o,pluginDetails:s}){let a=0,d=[...s.map(v=>{if(!v.options)return{config:{plugins:[v.plugin]},base:v.base,reference:v.reference};if("__isOptionsFunction"in v.plugin)return{config:{plugins:[v.plugin(v.options)]},base:v.base,reference:v.reference};throw new Error(`The plugin "${v.path}" does not accept options`)}),...o],{resolvedConfig:f}=Xe(t,[{config:ar(t.theme),base:r,reference:!0},...d,{config:{plugins:[ur]},base:r,reference:!0}]),{resolvedConfig:p,replacedThemeKeys:g}=Xe(t,d);t.resolveThemeValue=function(x,V){let S=y.theme(x,V);if(Array.isArray(S)&&S.length===2)return S[0];if(Array.isArray(S))return S.join(", ");if(typeof S=="string")return S};let h={designSystem:t,ast:n,resolvedConfig:f,featuresRef:{set current(v){a|=v}}},y=Ze({...h,referenceMode:!1}),w;for(let{handler:v,reference:x}of f.plugins)x?(w||=Ze({...h,referenceMode:!0}),v(w)):v(y);if(Lt(t,p,g),lr(t,p,g),dr(p,t),cr(p,t),sr(p,t),!t.theme.prefix&&f.prefix){if(f.prefix.endsWith("-")&&(f.prefix=f.prefix.slice(0,-1),console.warn(`The prefix "${f.prefix}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only and is written as a variant before all utilities. We have fixed up the prefix for you. Remove the trailing \`-\` to silence this warning.`)),!pn.test(f.prefix))throw new Error(`The prefix "${f.prefix}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only.`);t.theme.prefix=f.prefix}if(!t.important&&f.important===!0&&(t.important=!0),typeof f.important=="string"){let v=f.important;j(n,(x,{replaceWith:V,parent:S})=>{if(x.kind==="at-rule"&&!(x.name!=="@tailwind"||x.params!=="utilities"))return S?.kind==="rule"&&S.selector===v?2:(V(F(v,[x])),2)})}for(let v of f.blocklist)t.invalidCandidates.add(v);for(let v of f.content.files){if("raw"in v)throw new Error(`Error in the config file/plugin/preset. The \`content\` key contains a \`raw\` entry:

${JSON.stringify(v,null,2)}

This feature is not currently supported.`);e.push(v)}return a}var gn=/^[a-z]+$/;function mn(){throw new Error("No `loadModule` function provided to `compile`")}function hn(){throw new Error("No `loadStylesheet` function provided to `compile`")}function vn(t){let r=0,n=null;for(let e of D(t," "))e==="reference"?r|=2:e==="inline"?r|=1:e==="default"?r|=4:e.startsWith("prefix(")&&e.endsWith(")")&&(n=e.slice(7,-1));return[r,n]}var me=(c=>(c[c.None=0]="None",c[c.AtApply=1]="AtApply",c[c.AtImport=2]="AtImport",c[c.JsPluginCompat=4]="JsPluginCompat",c[c.ThemeFunction=8]="ThemeFunction",c[c.Utilities=16]="Utilities",c[c.Variants=32]="Variants",c))(me||{});async function mr(t,{base:r="",loadModule:n=mn,loadStylesheet:e=hn}={}){let o=0;t=[ee({base:r},t)],o|=await He(t,r,e);let s=null,a=new rt,c=[],d=[],f=null,p=null,g=[],h=[],y=null;j(t,(v,{parent:x,replaceWith:V,context:S})=>{if(v.kind==="at-rule"){if(v.name==="@tailwind"&&(v.params==="utilities"||v.params.startsWith("utilities"))){if(p!==null){V([]);return}let R=D(v.params," ");for(let E of R)if(E.startsWith("source(")){let P=E.slice(7,-1);if(P==="none"){y=P;continue}if(P[0]==='"'&&P[P.length-1]!=='"'||P[0]==="'"&&P[P.length-1]!=="'"||P[0]!=="'"&&P[0]!=='"')throw new Error("`source(\u2026)` paths must be quoted.");y={base:S.sourceBase??S.base,pattern:P.slice(1,-1)}}p=v,o|=16}if(v.name==="@utility"){if(x!==null)throw new Error("`@utility` cannot be nested.");if(v.nodes.length===0)throw new Error(`\`@utility ${v.params}\` is empty. Utilities should include at least one property.`);let R=Nt(v);if(R===null)throw new Error(`\`@utility ${v.params}\` defines an invalid utility name. Utilities should be alphanumeric and start with a lowercase letter.`);d.push(R)}if(v.name==="@source"){if(v.nodes.length>0)throw new Error("`@source` cannot have a body.");if(x!==null)throw new Error("`@source` cannot be nested.");let R=v.params;if(R[0]==='"'&&R[R.length-1]!=='"'||R[0]==="'"&&R[R.length-1]!=="'"||R[0]!=="'"&&R[0]!=='"')throw new Error("`@source` paths must be quoted.");h.push({base:S.base,pattern:R.slice(1,-1)}),V([]);return}if(v.name==="@variant"&&(x===null?v.nodes.length===0?v.name="@custom-variant":j(v.nodes,R=>{if(R.kind==="at-rule"&&R.name==="@slot")return v.name="@custom-variant",2}):g.push(v)),v.name==="@custom-variant"){if(x!==null)throw new Error("`@custom-variant` cannot be nested.");V([]);let[R,E]=D(v.params," ");if(!Ee.test(R))throw new Error(`\`@custom-variant ${R}\` defines an invalid variant name. Variants should only contain alphanumeric, dashes or underscore characters.`);if(v.nodes.length>0&&E)throw new Error(`\`@custom-variant ${R}\` cannot have both a selector and a body.`);if(v.nodes.length===0){if(!E)throw new Error(`\`@custom-variant ${R}\` has no selector or body.`);let P=D(E.slice(1,-1),","),i=[],u=[];for(let m of P)m=m.trim(),m[0]==="@"?i.push(m):u.push(m);c.push(m=>{m.variants.static(R,k=>{let b=[];u.length>0&&b.push(F(u.join(", "),k.nodes));for(let N of i)b.push(M(N,k.nodes));k.nodes=b},{compounds:ie([...u,...i])})});return}else{c.push(P=>{P.variants.fromAst(R,v.nodes)});return}}if(v.name==="@media"){let R=D(v.params," "),E=[];for(let P of R)if(P.startsWith("source(")){let i=P.slice(7,-1);j(v.nodes,(u,{replaceWith:m})=>{if(u.kind==="at-rule"&&u.name==="@tailwind"&&u.params==="utilities")return u.params+=` source(${i})`,m([ee({sourceBase:S.base},[u])]),2})}else if(P.startsWith("theme(")){let i=P.slice(6,-1);j(v.nodes,u=>{if(u.kind!=="at-rule")throw new Error('Files imported with `@import "\u2026" theme(\u2026)` must only contain `@theme` blocks.');if(u.name==="@theme")return u.params+=" "+i,1})}else if(P.startsWith("prefix(")){let i=P.slice(7,-1);j(v.nodes,u=>{if(u.kind==="at-rule"&&u.name==="@theme")return u.params+=` prefix(${i})`,1})}else P==="important"?s=!0:P==="reference"?v.nodes=[ee({reference:!0},v.nodes)]:E.push(P);return E.length>0?v.params=E.join(" "):R.length>0&&V(v.nodes),1}if(v.name==="@theme"){let[R,E]=vn(v.params);if(S.reference&&(R|=2),E){if(!gn.test(E))throw new Error(`The prefix "${E}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only.`);a.prefix=E}return j(v.nodes,(P,{replaceWith:i})=>{if(P.kind==="at-rule"&&P.name==="@keyframes")return a.addKeyframes(P),i([]),1;if(P.kind==="comment")return;if(P.kind==="declaration"&&P.property.startsWith("--")){a.add(P.property,P.value??"",R);return}let u=J([O(v.name,v.params,[P])]).split(`
`).map((m,k,b)=>`${k===0||k>=b.length-2?" ":">"} ${m}`).join(`
`);throw new Error(`\`@theme\` blocks must only contain custom properties or \`@keyframes\`.

${u}`)}),!f&&!(R&2)?(f=F(":root",v.nodes),V([f])):V([]),1}}});let w=Dt(a);s&&(w.important=s),o|=await gr({designSystem:w,base:r,ast:t,loadModule:n,globs:h});for(let v of c)v(w);for(let v of d)v(w);if(f){let v=[];for(let[V,S]of a.entries())S.options&2||v.push(l(V,S.value));let x=a.getKeyframes();if(x.length>0){let V=[...a.namespace("--animate").values()].flatMap(S=>S.split(" "));for(let S of x){let R=S.params;V.includes(R)&&v.push(_([S]))}}f.nodes=v}if(p){let v=p;v.kind="context",v.context={}}if(g.length>0){for(let v of g){let x=F("&",v.nodes),V=v.params,S=w.parseVariant(V);if(S===null)throw new Error(`Cannot use \`@variant\` with unknown variant: ${V}`);if(se(x,S,w.variants)===null)throw new Error(`Cannot use \`@variant\` with variant: ${V}`);Object.assign(v,x)}o|=32}return o|=ae(t,w),o|=ve(t,w),j(t,(v,{replaceWith:x})=>{if(v.kind==="at-rule")return v.name==="@utility"&&x([]),1}),{designSystem:w,ast:t,globs:h,root:y,utilitiesNode:p,features:o}}async function yn(t,r={}){let{designSystem:n,ast:e,globs:o,root:s,utilitiesNode:a,features:c}=await mr(t,r);e.unshift(Ne(`! tailwindcss v${ot} | MIT License | https://tailwindcss.com `));function d(h){n.invalidCandidates.add(h)}let f=new Set,p=null,g=0;return{globs:o,root:s,features:c,build(h){if(c===0)return t;if(!a)return p??=te(e),p;let y=!1,w=f.size;for(let x of h)n.invalidCandidates.has(x)||(f.add(x),y||=f.size!==w);if(!y)return p??=te(e),p;let v=Z(f,n,{onInvalidCandidate:d}).astNodes;return g===v.length?(p??=te(e),p):(g=v.length,a.nodes=v,p=te(e),p)}}}async function Xo(t,r={}){let n=X(t),e=await yn(n,r),o=n,s=t;return{...e,build(a){let c=e.build(a);return c===o||(s=J(c),o=c),s}}}async function el(t,r={}){return(await mr(X(t),r)).designSystem}function wn(){throw new Error("It looks like you're trying to use `tailwindcss` directly as a PostCSS plugin. The PostCSS plugin has moved to a separate package, so to continue using Tailwind CSS with PostCSS you'll need to install `@tailwindcss/postcss` and update your PostCSS configuration.")}export{me as Features,el as __unstable__loadDesignSystem,Xo as compile,yn as compileAst,wn as default};
