import React, { useState, useCallback } from 'react';
import type { VideoInfo } from '../types/video';

/**
 * État de téléchargement local
 */
interface DownloadState {
  isDownloading: boolean;
  progress: number;
  error: string | null;
  completed: boolean;
}

/**
 * Props pour le composant DownloadButton
 */
interface DownloadButtonProps {
  video: VideoInfo;
  onDownloadStart?: (video: VideoInfo) => void;
  onDownloadComplete?: (video: VideoInfo) => void;
  onDownloadError?: (video: VideoInfo, error: any) => void;
  disabled?: boolean;
  className?: string;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
}

/**
 * Composant DownloadButton - Bouton de téléchargement pour les vidéos
 * Respecte le principe SRP (Single Responsibility Principle)
 */
const DownloadButton: React.FC<DownloadButtonProps> = ({
  video,
  onDownloadStart,
  onDownloadComplete,
  onDownloadError,
  disabled = false,
  className = '',
  variant = 'primary',
  size = 'md'
}) => {
  const [downloadState, setDownloadState] = useState<DownloadState>({
    isDownloading: false,
    progress: 0,
    error: null,
    completed: false
  });

  /**
   * Gère le téléchargement d'une vidéo
   */
  const handleDownload = useCallback(async () => {
    if (disabled || downloadState.isDownloading) return;

    try {
      setDownloadState({
        isDownloading: true,
        progress: 0,
        error: null,
        completed: false
      });

      onDownloadStart?.(video);

      // Construire l'URL de la vidéo
      const videoUrl = video.url || `/videos/${video.filename}`;
      
      // Créer une requête pour télécharger le fichier
      const response = await fetch(videoUrl);
      
      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`);
      }

      // Obtenir la taille totale du fichier
      const contentLength = response.headers.get('content-length');
      const total = contentLength ? parseInt(contentLength, 10) : 0;

      // Lire le stream de données
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('Impossible de lire le fichier');
      }

      const chunks: Uint8Array[] = [];
      let received = 0;

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;
        
        chunks.push(value);
        received += value.length;

        // Mettre à jour le progrès
        if (total > 0) {
          const progress = (received / total) * 100;
          setDownloadState(prev => ({
            ...prev,
            progress: Math.round(progress)
          }));
        }
      }

      // Créer le blob et déclencher le téléchargement
      const blob = new Blob(chunks, { type: 'video/mp4' });
      const url = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = video.filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Nettoyer l'URL
      URL.revokeObjectURL(url);

      setDownloadState({
        isDownloading: false,
        progress: 100,
        error: null,
        completed: true
      });

      onDownloadComplete?.(video);

      // Réinitialiser après 2 secondes
      setTimeout(() => {
        setDownloadState(prev => ({
          ...prev,
          completed: false,
          progress: 0
        }));
      }, 2000);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur de téléchargement';
      
      setDownloadState({
        isDownloading: false,
        progress: 0,
        error: errorMessage,
        completed: false
      });

      onDownloadError?.(video, error);

      // Réinitialiser l'erreur après 3 secondes
      setTimeout(() => {
        setDownloadState(prev => ({
          ...prev,
          error: null
        }));
      }, 3000);
    }
  }, [video, disabled, downloadState.isDownloading, onDownloadStart, onDownloadComplete, onDownloadError]);

  /**
   * Classes CSS basées sur les props
   */
  const getButtonClasses = () => {
    const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';
    
    // Tailles
    const sizeClasses = {
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-sm',
      lg: 'px-6 py-3 text-base'
    };

    // Variantes
    const variantClasses = {
      primary: 'bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500',
      secondary: 'bg-gray-600 hover:bg-gray-700 text-white focus:ring-gray-500',
      outline: 'border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white focus:ring-blue-500'
    };

    // États
    const stateClasses = downloadState.isDownloading 
      ? 'opacity-75 cursor-not-allowed'
      : disabled 
        ? 'opacity-50 cursor-not-allowed'
        : 'hover:shadow-md active:scale-95';

    return `${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${stateClasses} ${className}`;
  };

  /**
   * Contenu du bouton selon l'état
   */
  const getButtonContent = () => {
    if (downloadState.completed) {
      return (
        <>
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
          Téléchargé
        </>
      );
    }

    if (downloadState.isDownloading) {
      return (
        <>
          <svg className="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          {downloadState.progress > 0 ? `${downloadState.progress}%` : 'Téléchargement...'}
        </>
      );
    }

    if (downloadState.error) {
      return (
        <>
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          Erreur
        </>
      );
    }

    return (
      <>
        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        Télécharger
      </>
    );
  };

  return (
    <div className="relative">
      <button
        onClick={handleDownload}
        disabled={disabled || downloadState.isDownloading}
        className={getButtonClasses()}
        title={`Télécharger ${video.displayName}`}
        aria-label={`Télécharger la vidéo ${video.displayName}`}
      >
        {getButtonContent()}
      </button>

      {/* Barre de progression */}
      {downloadState.isDownloading && downloadState.progress > 0 && (
        <div className="absolute -bottom-1 left-0 right-0 h-1 bg-gray-200 rounded-full overflow-hidden">
          <div 
            className="h-full bg-blue-600 transition-all duration-300 ease-out"
            style={{ width: `${downloadState.progress}%` }}
          />
        </div>
      )}

      {/* Message d'erreur */}
      {downloadState.error && (
        <div className="absolute top-full left-0 right-0 mt-1 p-2 bg-red-100 border border-red-300 rounded text-red-700 text-xs z-10">
          {downloadState.error}
        </div>
      )}
    </div>
  );
};

export default DownloadButton;
